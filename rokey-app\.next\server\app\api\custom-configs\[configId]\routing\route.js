/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/custom-configs/[configId]/routing/route";
exports.ids = ["app/api/custom-configs/[configId]/routing/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&page=%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&page=%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_custom_configs_configId_routing_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/custom-configs/[configId]/routing/route.ts */ \"(rsc)/./src/app/api/custom-configs/[configId]/routing/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/custom-configs/[configId]/routing/route\",\n        pathname: \"/api/custom-configs/[configId]/routing\",\n        filename: \"route\",\n        bundlePath: \"app/api/custom-configs/[configId]/routing/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\custom-configs\\\\[configId]\\\\routing\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_custom_configs_configId_routing_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&page=%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/custom-configs/[configId]/routing/route.ts":
/*!****************************************************************!*\
  !*** ./src/app/api/custom-configs/[configId]/routing/route.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-client */ \"(rsc)/./src/lib/stripe-client.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n// Define the allowed routing strategy types\nconst ALLOWED_ROUTING_STRATEGIES = [\n    'none',\n    'intelligent_role',\n    'complexity_round_robin',\n    'auto_optimal',\n    'strict_fallback',\n    'cost_optimized',\n    'ab_routing',\n    'agent_mode'\n];\nconst RoutingStrategyEnum = zod__WEBPACK_IMPORTED_MODULE_3__.z[\"enum\"](ALLOWED_ROUTING_STRATEGIES);\nconst UpdateRoutingSettingsSchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    routing_strategy: RoutingStrategyEnum,\n    routing_strategy_params: zod__WEBPACK_IMPORTED_MODULE_3__.z.record(zod__WEBPACK_IMPORTED_MODULE_3__.z.any()).nullable().optional()\n});\nasync function PUT(request, { params }) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { configId } = await params;\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in PUT routing settings:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to update routing settings.'\n        }, {\n            status: 401\n        });\n    }\n    if (!configId || typeof configId !== 'string') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid configuration ID.'\n        }, {\n            status: 400\n        });\n    }\n    let requestBody;\n    try {\n        requestBody = await request.json();\n    } catch (e) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid JSON request body.'\n        }, {\n            status: 400\n        });\n    }\n    const validationResult = UpdateRoutingSettingsSchema.safeParse(requestBody);\n    if (!validationResult.success) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid request body.',\n            issues: validationResult.error.flatten().fieldErrors\n        }, {\n            status: 400\n        });\n    }\n    const { routing_strategy, routing_strategy_params } = validationResult.data;\n    try {\n        // Check user's subscription tier for advanced routing access\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', session.user.id).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Define which strategies require advanced routing access\n        const advancedStrategies = [\n            'intelligent_role',\n            'complexity_round_robin',\n            'auto_optimal',\n            'cost_optimized',\n            'ab_routing'\n        ];\n        // Free tier can only use 'none' and 'strict_fallback'\n        if (userTier === 'free' && advancedStrategies.includes(routing_strategy)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `The ${routing_strategy} routing strategy is not available on the free plan. Please upgrade to access advanced routing strategies.`\n            }, {\n                status: 403\n            });\n        }\n        // Check if user has access to advanced routing features\n        if (advancedStrategies.includes(routing_strategy) && !(0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.hasFeatureAccess)(userTier, 'advanced_routing')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Advanced routing strategies are not available on the ${userTier} plan. Please upgrade to access this feature.`\n            }, {\n                status: 403\n            });\n        }\n        // Verify the user owns the custom_api_config or is an admin (for future use)\n        // const { data: configData, error: configError } = await supabase\n        //   .from('custom_api_configs')\n        //   .select('id, user_id')\n        //   .eq('id', configId)\n        //   .eq('user_id', user.id) // Ensure the authenticated user owns this config\n        //   .single();\n        // if (configError || !configData) {\n        //   if (configError && configError.code === 'PGRST116') { // No rows found\n        //     return NextResponse.json({ error: 'Configuration not found or you do not have permission to modify it.' }, { status: 404 });\n        //   }\n        //   console.error('Error fetching config for permission check:', configError);\n        //   return NextResponse.json({ error: 'Error verifying configuration ownership.' }, { status: 500 });\n        // }\n        // Perform the update\n        const { error: updateError } = await supabase.from('custom_api_configs').update({\n            routing_strategy: routing_strategy,\n            routing_strategy_params: routing_strategy_params,\n            updated_at: new Date().toISOString()\n        }).eq('id', configId).eq('user_id', session.user.id); // Ensure user owns the config for RLS compliance\n        if (updateError) {\n            console.error('Error updating routing settings in Supabase:', updateError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to update routing settings.',\n                details: updateError.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Routing settings updated successfully.',\n            routing_strategy,\n            routing_strategy_params\n        }, {\n            status: 200\n        });\n    } catch (err) {\n        console.error('Unexpected error in PUT /custom-configs/[configId]/routing:', err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected server error occurred.',\n            details: err.message\n        }, {\n            status: 500\n        });\n    }\n}\n// Optional: OPTIONS handler for CORS preflight if your frontend is on a different domain/port during dev\nasync function OPTIONS() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({}, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'PUT, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/custom-configs/[configId]/routing/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-client.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-client.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TIER_CONFIGS: () => (/* binding */ TIER_CONFIGS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getPriceIdForTier: () => (/* binding */ getPriceIdForTier),\n/* harmony export */   getTierConfig: () => (/* binding */ getTierConfig),\n/* harmony export */   getTierFromPriceId: () => (/* binding */ getTierFromPriceId),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess)\n/* harmony export */ });\n/* harmony import */ var _stripe_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n// Client-safe Stripe utilities (no server-side Stripe instance)\n\nconst TIER_CONFIGS = {\n    free: {\n        name: 'Free',\n        price: '$0',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.FREE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.FREE,\n        features: [\n            'Unlimited API requests',\n            '1 Custom Configuration',\n            '3 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback routing only',\n            'Basic analytics only',\n            'No custom roles, basic router only',\n            'Limited logs',\n            'Community support'\n        ],\n        limits: {\n            configurations: 1,\n            apiKeysPerConfig: 3,\n            apiRequests: 999999,\n            canUseAdvancedRouting: false,\n            canUseCustomRoles: false,\n            maxCustomRoles: 0,\n            canUsePromptEngineering: false,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    starter: {\n        name: 'Starter',\n        price: '$20',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.STARTER,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.STARTER,\n        features: [\n            'Unlimited API requests',\n            '15 Custom Configurations',\n            '5 API Keys per config',\n            'All 300+ AI models',\n            'Intelligent routing strategies',\n            'Up to 3 custom roles',\n            'Intelligent role routing',\n            'Prompt engineering (no file upload)',\n            'Enhanced logs and analytics',\n            'Community support'\n        ],\n        limits: {\n            configurations: 15,\n            apiKeysPerConfig: 5,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 3,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    professional: {\n        name: 'Professional',\n        price: '$50',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.PROFESSIONAL,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.PROFESSIONAL,\n        features: [\n            'Unlimited API requests',\n            'Unlimited Custom Configurations',\n            'Unlimited API Keys per config',\n            'All 300+ AI models',\n            'All advanced routing strategies',\n            'Unlimited custom roles',\n            'Prompt engineering + Knowledge base (5 documents)',\n            'Semantic caching',\n            'Advanced analytics and logging',\n            'Priority email support'\n        ],\n        limits: {\n            configurations: 999999,\n            apiKeysPerConfig: 999999,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 5,\n            canUseSemanticCaching: true\n        }\n    }\n};\nfunction getTierConfig(tier) {\n    return TIER_CONFIGS[tier];\n}\nfunction getPriceIdForTier(tier) {\n    return TIER_CONFIGS[tier].priceId;\n}\nfunction getTierFromPriceId(priceId) {\n    for (const [tier, config] of Object.entries(TIER_CONFIGS)){\n        if (config.priceId === priceId) {\n            return tier;\n        }\n    }\n    return 'free'; // Default fallback to free tier\n}\nfunction formatPrice(tier) {\n    return TIER_CONFIGS[tier].price;\n}\nfunction canPerformAction(tier, action, currentCount) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(action){\n        case 'create_config':\n            return currentCount < limits.configurations;\n        case 'create_api_key':\n            return currentCount < limits.apiKeysPerConfig;\n        default:\n            return true;\n    }\n}\nfunction hasFeatureAccess(tier, feature) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(feature){\n        case 'custom_roles':\n            return limits.canUseCustomRoles;\n        case 'knowledge_base':\n            return limits.canUseKnowledgeBase;\n        case 'advanced_routing':\n            return limits.canUseAdvancedRouting;\n        case 'prompt_engineering':\n            return limits.canUsePromptEngineering;\n        case 'semantic_caching':\n            return limits.canUseSemanticCaching;\n        case 'configurations':\n            return limits.configurations > 0; // All tiers can create at least some configurations\n        default:\n            return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: isProduction ? process.env.STRIPE_LIVE_WEBHOOK_SECRET : process.env.STRIPE_TEST_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',\n        secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined',\n        webhook: STRIPE_KEYS.webhookSecret ? STRIPE_KEYS.webhookSecret.substring(0, 15) + '...' : 'undefined'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&page=%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();