// True Agent Collaboration - Conversation Supervisor
// Orchestrates the entire conversation flow and ensures quality collaboration

import { StateGraph, START, END } from '@langchain/langgraph';
import { ConversationStateManager } from './ConversationState';
import { ConversationNodes, ConversationGraphState, ApiKey } from './ConversationNodes';

export interface CollaborationResult {
  success: boolean;
  finalResponse?: string;
  agentNames?: string[];
  error?: string;
  qualityMetrics?: {
    overallScore: number;
    participationBalance: number;
    iterativeImprovement: number;
    consensusStrength: number;
    noveltyScore: number;
  };
  conversationSummary?: string;
}

export class ConversationSupervisor {
  private conversationManager: ConversationStateManager;
  private agentApiKeys: Record<string, ApiKey>;
  private customApiConfigId: string;
  private maxIterations: number;

  constructor(
    conversationId: string,
    userPrompt: string,
    agentApiKeys: Record<string, ApiKey>,
    customApiConfigId: string,
    maxIterations: number = 10
  ) {
    // Initialize conversation manager
    const agents = Object.entries(agentApiKeys).map(([id, key]) => ({
      id,
      label: key.label,
      role: `Agent ${id.replace('agent_', '')} (${key.label})`
    }));

    this.conversationManager = new ConversationStateManager(
      conversationId,
      userPrompt,
      agents,
      Math.ceil(maxIterations / 2), // Max rounds is half of max iterations
      3 // Max debate rounds - force consensus after 3 rounds of debate
    );

    this.agentApiKeys = agentApiKeys;
    this.customApiConfigId = customApiConfigId;
    this.maxIterations = maxIterations;
  }

  /**
   * Execute the full conversation workflow
   */
  async executeCollaboration(): Promise<CollaborationResult> {
    console.log(`[Conversation Supervisor] 🚀 Starting true agent collaboration with ${Object.keys(this.agentApiKeys).length} agents`);
    
    try {
      // Build the conversation workflow
      const workflow = this.buildConversationWorkflow();
      
      // Initial state
      const initialState: ConversationGraphState = {
        messages: [],
        conversationManager: this.conversationManager,
        agentApiKeys: this.agentApiKeys,
        customApiConfigId: this.customApiConfigId,
        iterationCount: 0,
        maxIterations: this.maxIterations,
        nextAction: 'continue_discussion'
      };

      // Execute the workflow
      console.log(`[Conversation Supervisor] 📋 Executing conversation workflow...`);
      const finalState = await workflow.invoke(initialState);

      // Extract results
      const finalSolution = this.conversationManager.getState().finalSolution;
      const qualityMetrics = this.conversationManager.getQualityMetrics();
      const agentNames = Object.values(this.agentApiKeys).map(key => key.label);

      if (!finalSolution) {
        console.error(`[Conversation Supervisor] ❌ No final solution generated`);
        return {
          success: false,
          error: 'Collaboration completed but no final solution was generated',
          agentNames
        };
      }

      // Generate the final collaborative response
      const finalResponse = this.generateCollaborativeResponse(finalSolution, qualityMetrics);

      console.log(`[Conversation Supervisor] ✅ Collaboration completed successfully!`);
      console.log(`[Conversation Supervisor] 📊 Quality Score: ${qualityMetrics.overallScore.toFixed(2)}/1.0`);

      return {
        success: true,
        finalResponse,
        agentNames,
        qualityMetrics,
        conversationSummary: this.generateConversationSummary()
      };

    } catch (error) {
      console.error(`[Conversation Supervisor] ❌ Collaboration failed:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        agentNames: Object.values(this.agentApiKeys).map(key => key.label)
      };
    }
  }

  /**
   * Build the LangGraph conversation workflow
   */
  private buildConversationWorkflow() {
    const workflow = new StateGraph<ConversationGraphState>({
      channels: {
        messages: {
          value: (x: any[], y: any[]) => x.concat(y),
          default: () => []
        },
        conversationManager: {
          value: (x: any, y: any) => y ?? x,
          default: () => this.conversationManager
        },
        agentApiKeys: {
          value: (x: any, y: any) => y ?? x,
          default: () => this.agentApiKeys
        },
        customApiConfigId: {
          value: (x: any, y: any) => y ?? x,
          default: () => this.customApiConfigId
        },
        currentAgent: {
          value: (x: any, y: any) => y ?? x,
          default: () => undefined
        },
        nextAction: {
          value: (x: any, y: any) => y ?? x,
          default: () => 'continue_discussion'
        },
        iterationCount: {
          value: (x: number, y: number) => y ?? x,
          default: () => 0
        },
        maxIterations: {
          value: (x: number, y: number) => y ?? x,
          default: () => this.maxIterations
        }
      }
    });

    // Add conversation phase nodes
    workflow.addNode('brainstorming', ConversationNodes.createBrainstormingNode());
    workflow.addNode('debate', ConversationNodes.createDebateNode());
    workflow.addNode('synthesis', ConversationNodes.createSynthesisNode());
    workflow.addNode('validation', ConversationNodes.createValidationNode());
    workflow.addNode('supervisor', this.createSupervisorNode());

    // Define the conversation flow
    workflow.addEdge(START, 'brainstorming');
    workflow.addEdge('brainstorming', 'supervisor');
    workflow.addEdge('debate', 'supervisor');
    workflow.addEdge('synthesis', 'supervisor');
    workflow.addEdge('validation', END);

    // Supervisor decides the next phase
    workflow.addConditionalEdges(
      'supervisor',
      this.createConversationRouter(),
      {
        'continue_debate': 'debate',
        'move_to_synthesis': 'synthesis',
        'move_to_validation': 'validation',
        'complete': END
      }
    );

    return workflow.compile();
  }

  /**
   * Create the supervisor node that manages conversation flow
   */
  private createSupervisorNode() {
    return async (state: ConversationGraphState): Promise<Partial<ConversationGraphState>> => {
      console.log(`[Supervisor] 👑 Evaluating conversation progress...`);
      
      const manager = state.conversationManager;
      const currentPhase = manager.getCurrentPhase();
      const qualityMetrics = manager.getQualityMetrics();
      const unresolvedIssues = manager.getUnresolvedIssues();
      
      console.log(`[Supervisor] Phase: ${currentPhase}, Quality: ${qualityMetrics.overallScore.toFixed(2)}, Issues: ${unresolvedIssues.length}`);
      
      // Determine next action based on conversation state
      let nextAction: string;

      if (currentPhase === 'brainstorming') {
        // Move to debate if we have enough ideas
        const ideaCount = manager.getState().conversationHistory.filter(turn => turn.messageType === 'proposal').length;
        if (ideaCount >= Object.keys(state.agentApiKeys).length) {
          manager.advancePhase(); // Advance to debate
          nextAction = 'continue_debate';
        } else {
          nextAction = 'continue_debate';
        }
      } else if (currentPhase === 'debate') {
        // Check if we've reached max debate rounds
        const hasReachedMaxDebateRounds = manager.hasReachedMaxDebateRounds();
        const debateInfo = manager.getDebateRoundInfo();

        console.log(`[Supervisor] Debate round ${debateInfo.current}/${debateInfo.max}, Issues: ${unresolvedIssues.length}`);

        // Force move to synthesis if max debate rounds reached
        if (hasReachedMaxDebateRounds) {
          console.log(`[Supervisor] 🛑 Max debate rounds (${debateInfo.max}) reached, forcing consensus`);
          manager.advancePhase(); // Advance to synthesis
          nextAction = 'move_to_synthesis';
        } else {
          // Continue debate if we have significant issues and haven't hit limits
          const shouldContinueDebate = unresolvedIssues.length > 1 && state.iterationCount < state.maxIterations * 0.6;
          if (shouldContinueDebate) {
            manager.nextDebateRound(); // Increment debate round counter
            nextAction = 'continue_debate';
          } else {
            manager.advancePhase(); // Advance to synthesis
            nextAction = 'move_to_synthesis';
          }
        }
      } else if (currentPhase === 'synthesis') {
        // Check if synthesis has been completed (final solution exists)
        const finalSolution = manager.getState().finalSolution;
        if (finalSolution) {
          manager.advancePhase(); // Advance to validation
          nextAction = 'move_to_validation';
        } else {
          // Stay in synthesis until we have a final solution
          nextAction = 'move_to_synthesis';
        }
      } else {
        // Complete the conversation
        nextAction = 'complete';
      }
      
      console.log(`[Supervisor] 🎯 Decision: ${nextAction}`);
      
      return {
        nextAction: nextAction as any,
        iterationCount: state.iterationCount + 1
      };
    };
  }

  /**
   * Create the conversation router that decides next steps
   */
  private createConversationRouter() {
    return (state: ConversationGraphState): string => {
      const manager = state.conversationManager;
      const currentPhase = manager.getCurrentPhase();
      const nextAction = state.nextAction;
      
      // Safety check for max iterations
      if (state.iterationCount >= state.maxIterations) {
        console.log(`[Router] 🛑 Max iterations reached, moving to completion`);
        return 'complete';
      }
      
      // Route based on supervisor decision
      switch (nextAction) {
        case 'continue_discussion':
          if (currentPhase === 'brainstorming') return 'continue_debate';
          if (currentPhase === 'debate') return 'continue_debate';
          return 'move_to_synthesis';
        
        case 'advance_phase':
          if (currentPhase === 'brainstorming') return 'continue_debate';
          if (currentPhase === 'debate') return 'move_to_synthesis';
          if (currentPhase === 'synthesis') return 'move_to_validation';
          return 'complete';
        
        case 'complete':
          return 'complete';
        
        default:
          return nextAction || 'complete';
      }
    };
  }

  /**
   * Generate the final collaborative response with proper formatting
   */
  private generateCollaborativeResponse(finalSolution: string, qualityMetrics: any): string {
    const agentCount = Object.keys(this.agentApiKeys).length;
    const conversationHistory = this.conversationManager.getState().conversationHistory;
    const consensusItems = this.conversationManager.getConsensusItems();
    
    // Extract key contributions from each agent
    const agentContributions = Object.entries(this.agentApiKeys).map(([agentId, agentKey]) => {
      const agentTurns = conversationHistory.filter(turn => turn.agent === agentId);
      const keyContribution = agentTurns.find(turn => turn.messageType === 'proposal' || turn.messageType === 'synthesis');
      return {
        agent: agentKey.label,
        contribution: keyContribution?.message.substring(0, 200) + '...' || 'Participated in discussion'
      };
    });

    return `# 🤖 **True Agent Collaboration Complete**

*This response was created through genuine multi-agent collaboration with real-time discussion, debate, and consensus building between ${agentCount} AI agents.*

## 📋 **Collaborative Solution**

${finalSolution}

## 🗣️ **Agent Contributions & Real Discussion**

${agentContributions.map(contrib => 
  `**${contrib.agent}:** ${contrib.contribution}`
).join('\n\n')}

## 🤝 **Genuine Collaboration Process**

**✅ Brainstorming Phase:** All agents shared initial ideas and explored the problem space together
**✅ Debate Phase:** Agents challenged each other's ideas, identified conflicts, and refined thinking through real discussion
**✅ Synthesis Phase:** Team collaborated to resolve disagreements and build the final solution
**✅ Validation Phase:** All agents validated and approved the final collaborative result

## 📊 **Collaboration Quality Metrics**

- **Overall Quality Score:** ${(qualityMetrics.overallScore * 100).toFixed(0)}%
- **Participation Balance:** ${(qualityMetrics.participationBalance * 100).toFixed(0)}%
- **Iterative Improvement:** ${(qualityMetrics.iterativeImprovement * 100).toFixed(0)}%
- **Consensus Strength:** ${(qualityMetrics.consensusStrength * 100).toFixed(0)}%

## 🎯 **Team Consensus Points**

${consensusItems.length > 0 ? 
  consensusItems.map(item => `- **${item.topic}:** ${item.agreedPoint}`).join('\n') :
  '- The team reached consensus through iterative discussion and debate'
}

**Participating Agents:** ${Object.values(this.agentApiKeys).map(key => key.label).join(', ')}

**Collaboration Type:** True multi-agent conversation with real-time discussion, conflict resolution, and consensus building

---

*This solution represents genuine collective intelligence achieved through structured agent-to-agent collaboration, not simulated teamwork.*`;
  }

  /**
   * Generate a summary of the conversation for debugging/analysis
   */
  private generateConversationSummary(): string {
    const state = this.conversationManager.getState();
    const turnCount = state.conversationHistory.length;
    const phaseDistribution = state.conversationHistory.reduce((acc, turn) => {
      acc[turn.messageType] = (acc[turn.messageType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return `
Conversation Summary:
- Total Turns: ${turnCount}
- Phases Completed: ${state.currentPhase}
- Consensus Items: ${state.consensusItems.length}
- Unresolved Issues: ${state.unresolvedIssues.length}
- Turn Distribution: ${JSON.stringify(phaseDistribution)}
- Quality Score: ${state.qualityMetrics.overallScore.toFixed(2)}
- Duration: ${((Date.now() - state.startTime) / 1000).toFixed(1)}s
`.trim();
  }
}
