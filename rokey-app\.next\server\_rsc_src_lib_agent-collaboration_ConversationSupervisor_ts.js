"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_agent-collaboration_ConversationSupervisor_ts";
exports.ids = ["_rsc_src_lib_agent-collaboration_ConversationSupervisor_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/agent-collaboration/ConversationNodes.ts":
/*!**********************************************************!*\
  !*** ./src/lib/agent-collaboration/ConversationNodes.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationNodes: () => (/* binding */ ConversationNodes)\n/* harmony export */ });\n/* harmony import */ var _providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../providers/executeProviderRequest */ \"(rsc)/./src/lib/providers/executeProviderRequest.ts\");\n/* harmony import */ var _encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../encryption */ \"(rsc)/./src/lib/encryption.ts\");\n// True Agent Collaboration - LangGraph Conversation Nodes\n// These nodes handle different phases of real agent-to-agent conversation\n\n\nclass ConversationNodes {\n    /**\n   * Brainstorming Phase Node - Agents share initial ideas and explore the problem space\n   */ static createBrainstormingNode() {\n        return async (state)=>{\n            console.log(`[Brainstorming] Starting brainstorming phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Each agent contributes initial ideas\n            for (const agentId of agentKeys){\n                const agentKey = state.agentApiKeys[agentId];\n                const contextSummary = manager.generateContextSummary();\n                const brainstormPrompt = `${contextSummary}\n\n[BRAINSTORMING PHASE - INITIAL IDEAS]\nYou are ${manager.getState().agentRoles[agentId]} participating in a collaborative brainstorming session.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nYour task in this brainstorming phase:\n1. **ANALYZE** the request and share your initial understanding\n2. **PROPOSE** creative approaches and ideas\n3. **IDENTIFY** key challenges and opportunities\n4. **ASK QUESTIONS** that will help the team understand the problem better\n5. **BUILD** on any ideas already shared by other agents\n\n${manager.getRecentTurns(3).length > 0 ? 'Respond to and build on the ideas already shared by your teammates.' : 'You are starting the brainstorming - share your initial thoughts and ideas.'}\n\nFocus on generating diverse, creative ideas rather than detailed solutions. Be collaborative and engaging!`;\n                try {\n                    const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                        custom_api_config_id: state.customApiConfigId,\n                        messages: [\n                            {\n                                role: 'user',\n                                content: brainstormPrompt\n                            }\n                        ],\n                        temperature: 0.8,\n                        max_tokens: 2000,\n                        stream: false,\n                        role: 'orchestration'\n                    });\n                    if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                        const response = result.responseData.choices[0].message.content;\n                        // Add to conversation\n                        manager.addTurn({\n                            agent: agentId,\n                            agentLabel: agentKey.label,\n                            message: response,\n                            messageType: 'proposal',\n                            respondingTo: manager.getRecentTurns(1)[0]?.id // Respond to most recent if exists\n                        });\n                        console.log(`[Brainstorming] ✅ ${agentKey.label} contributed ideas (${response.length} chars)`);\n                    } else {\n                        console.error(`[Brainstorming] ❌ ${agentKey.label} failed to contribute`);\n                    }\n                } catch (error) {\n                    console.error(`[Brainstorming] Error with ${agentKey.label}:`, error);\n                }\n            }\n            // Update phase progress\n            manager.setPhaseProgress('brainstorming', 1.0);\n            manager.updateQualityMetrics();\n            return {\n                nextAction: 'advance_phase',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Debate Phase Node - Agents challenge ideas, identify conflicts, and refine thinking\n   */ static createDebateNode() {\n        return async (state)=>{\n            console.log(`[Debate] Starting debate phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Ensure we're in debate phase (don't advance here, let supervisor manage phases)\n            console.log(`[Debate] Current phase: ${manager.getCurrentPhase()}`);\n            // Each agent critiques and challenges ideas\n            for (const agentId of agentKeys){\n                const agentKey = state.agentApiKeys[agentId];\n                const contextSummary = manager.generateContextSummary();\n                const recentTurns = manager.getRecentTurns(5);\n                const debatePrompt = `${contextSummary}\n\n[DEBATE PHASE - CRITICAL ANALYSIS]\nYou are ${manager.getState().agentRoles[agentId]} in a critical debate phase.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nRecent Team Discussion:\n${recentTurns.map((turn)=>`${turn.agentLabel}: ${turn.message}`).join('\\n\\n')}\n\nYour task in this debate phase:\n1. **CRITIQUE** ideas shared by other agents - identify flaws, gaps, or concerns\n2. **CHALLENGE** assumptions and approaches that might not work\n3. **DEFEND** good ideas and explain why they're valuable\n4. **IDENTIFY** conflicts between different approaches\n5. **PROPOSE** alternative solutions to problems you identify\n6. **ASK HARD QUESTIONS** that need to be resolved\n\nBe constructively critical - challenge ideas to make them better, not to tear them down. Focus on finding the best path forward through rigorous analysis.`;\n                try {\n                    const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                        custom_api_config_id: state.customApiConfigId,\n                        messages: [\n                            {\n                                role: 'user',\n                                content: debatePrompt\n                            }\n                        ],\n                        temperature: 0.6,\n                        max_tokens: 2500,\n                        stream: false,\n                        role: 'orchestration'\n                    });\n                    if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                        const response = result.responseData.choices[0].message.content;\n                        // Analyze response for conflicts and issues\n                        const hasDisagreement = response.toLowerCase().includes('disagree') || response.toLowerCase().includes('concern') || response.toLowerCase().includes('problem');\n                        if (hasDisagreement) {\n                            // Only add new issues if we haven't reached max debate rounds\n                            const debateInfo = manager.getDebateRoundInfo();\n                            if (debateInfo.current < debateInfo.max) {\n                                // Extract the main concern/issue - be more selective\n                                const issueMatch = response.match(/(?:major concern|critical issue|fundamental problem|serious disagreement)[^.]*\\.?/i);\n                                if (issueMatch && issueMatch[0].length > 20) {\n                                    const existingIssues = manager.getUnresolvedIssues();\n                                    const isDuplicate = existingIssues.some((issue)=>issue.description.toLowerCase().includes(issueMatch[0].toLowerCase().substring(0, 15)));\n                                    if (!isDuplicate) {\n                                        manager.addUnresolvedIssue(issueMatch[0], [\n                                            agentId\n                                        ], 'medium');\n                                    }\n                                }\n                            } else {\n                                console.log(`[Debate] 🛑 Max debate rounds reached, not adding new issues`);\n                            }\n                        }\n                        manager.addTurn({\n                            agent: agentId,\n                            agentLabel: agentKey.label,\n                            message: response,\n                            messageType: hasDisagreement ? 'critique' : 'improvement',\n                            respondingTo: recentTurns[recentTurns.length - 1]?.id\n                        });\n                        console.log(`[Debate] ✅ ${agentKey.label} provided critical analysis (${response.length} chars)`);\n                    }\n                } catch (error) {\n                    console.error(`[Debate] Error with ${agentKey.label}:`, error);\n                }\n            }\n            manager.setPhaseProgress('debate', 1.0);\n            manager.updateQualityMetrics();\n            // Check if we need another round of debate or can move to synthesis\n            const unresolvedIssues = manager.getUnresolvedIssues();\n            if (unresolvedIssues.length > 0 && state.iterationCount < state.maxIterations) {\n                return {\n                    nextAction: 'continue_discussion',\n                    iterationCount: state.iterationCount + 1\n                };\n            }\n            return {\n                nextAction: 'advance_phase',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Synthesis Phase Node - Agents collaborate to build the final solution\n   */ static createSynthesisNode() {\n        return async (state)=>{\n            console.log(`[Synthesis] Starting synthesis phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Log current phase (let supervisor manage phase transitions)\n            console.log(`[Synthesis] Current phase: ${manager.getCurrentPhase()}`);\n            // First, try to resolve any remaining issues\n            const unresolvedIssues = manager.getUnresolvedIssues();\n            const hasReachedMaxDebateRounds = manager.hasReachedMaxDebateRounds();\n            if (unresolvedIssues.length > 0) {\n                if (hasReachedMaxDebateRounds) {\n                    console.log(`[Synthesis] 🛑 Max debate rounds reached - forcing consensus on ${unresolvedIssues.length} remaining issues`);\n                    // Force resolution of all remaining issues\n                    for (const issue of unresolvedIssues){\n                        manager.resolveIssue(issue.id, \"Consensus reached due to debate round limit - proceeding with majority approach\");\n                    }\n                } else {\n                    console.log(`[Synthesis] Resolving ${unresolvedIssues.length} unresolved issues first`);\n                }\n                // Only do detailed issue resolution if we haven't forced consensus\n                if (!hasReachedMaxDebateRounds && unresolvedIssues.length > 0) {\n                    for (const issue of unresolvedIssues.slice(0, 3)){\n                        for (const agentId of agentKeys){\n                            const agentKey = state.agentApiKeys[agentId];\n                            const resolutionPrompt = `[ISSUE RESOLUTION]\nYou are ${manager.getState().agentRoles[agentId]} helping resolve a team disagreement.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nUnresolved Issue: \"${issue.description}\"\n\nContext: ${manager.generateContextSummary()}\n\nYour task:\n1. **PROPOSE** a specific solution to resolve this issue\n2. **EXPLAIN** why your solution addresses the core concern\n3. **CONSIDER** how this fits with the overall approach\n4. **BUILD CONSENSUS** by finding common ground\n\nFocus on finding a solution that the whole team can agree on.`;\n                            try {\n                                const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                                    custom_api_config_id: state.customApiConfigId,\n                                    messages: [\n                                        {\n                                            role: 'user',\n                                            content: resolutionPrompt\n                                        }\n                                    ],\n                                    temperature: 0.4,\n                                    max_tokens: 1500,\n                                    stream: false,\n                                    role: 'orchestration'\n                                });\n                                if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                                    const response = result.responseData.choices[0].message.content;\n                                    manager.addTurn({\n                                        agent: agentId,\n                                        agentLabel: agentKey.label,\n                                        message: response,\n                                        messageType: 'agreement',\n                                        respondingTo: undefined\n                                    });\n                                    // Check if this resolves the issue\n                                    if (response.toLowerCase().includes('agree') || response.toLowerCase().includes('solution')) {\n                                        manager.resolveIssue(issue.id, response);\n                                    }\n                                }\n                            } catch (error) {\n                                console.error(`[Synthesis] Error resolving issue with ${agentKey.label}:`, error);\n                            }\n                        }\n                    }\n                } // End of detailed issue resolution conditional\n            }\n            // Now build the final collaborative solution\n            console.log(`[Synthesis] Building final collaborative solution`);\n            const leadAgent = agentKeys[0]; // Use first agent as synthesis coordinator\n            const leadAgentKey = state.agentApiKeys[leadAgent];\n            const synthesisPrompt = `[FINAL SOLUTION SYNTHESIS]\nYou are ${manager.getState().agentRoles[leadAgent]} coordinating the final solution synthesis.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nComplete Team Discussion:\n${manager.getState().conversationHistory.map((turn)=>`${turn.agentLabel} (${turn.messageType}): ${turn.message}`).join('\\n\\n')}\n\nTeam Consensus Points:\n${manager.getConsensusItems().map((item)=>`- ${item.topic}: ${item.agreedPoint}`).join('\\n')}\n\nYour task:\n1. **SYNTHESIZE** all the team's ideas into one comprehensive solution\n2. **INCORPORATE** the best elements from each agent's contributions\n3. **ADDRESS** the original request completely and thoroughly\n4. **ENSURE** the solution reflects the team's consensus and collaboration\n5. **PROVIDE** a high-quality, detailed response that showcases the collective intelligence\n\nCreate the final solution that represents the best collaborative thinking of the entire team.`;\n            try {\n                const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(leadAgentKey.provider, leadAgentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(leadAgentKey.encrypted_api_key), {\n                    custom_api_config_id: state.customApiConfigId,\n                    messages: [\n                        {\n                            role: 'user',\n                            content: synthesisPrompt\n                        }\n                    ],\n                    temperature: 0.3,\n                    max_tokens: 4000,\n                    stream: false,\n                    role: 'orchestration'\n                });\n                if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                    const finalSolution = result.responseData.choices[0].message.content;\n                    console.log(`[Synthesis] 🎯 Setting final solution (${finalSolution.length} chars)`);\n                    manager.setFinalSolution(finalSolution);\n                    manager.addTurn({\n                        agent: leadAgent,\n                        agentLabel: leadAgentKey.label,\n                        message: finalSolution,\n                        messageType: 'synthesis'\n                    });\n                    console.log(`[Synthesis] ✅ Final solution synthesized and stored successfully`);\n                } else {\n                    console.error(`[Synthesis] ❌ Failed to get final solution from lead agent:`, result.error);\n                }\n            } catch (error) {\n                console.error(`[Synthesis] ❌ Error creating final solution:`, error);\n            }\n            manager.setPhaseProgress('synthesis', 1.0);\n            manager.updateQualityMetrics();\n            return {\n                nextAction: 'advance_phase',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Validation Phase Node - Team validates the final solution\n   */ static createValidationNode() {\n        return async (state)=>{\n            console.log(`[Validation] Starting validation phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Log current phase (let supervisor manage phase transitions)\n            console.log(`[Validation] Current phase: ${manager.getCurrentPhase()}`);\n            const finalSolution = manager.getState().finalSolution;\n            console.log(`[Validation] 🔍 Checking for final solution... Found: ${finalSolution ? 'YES' : 'NO'}`);\n            if (!finalSolution) {\n                console.error(`[Validation] ❌ No final solution to validate - synthesis may have failed`);\n                console.error(`[Validation] 📊 Current state: Phase=${manager.getCurrentPhase()}, History=${manager.getState().conversationHistory.length} turns`);\n                return {\n                    nextAction: 'complete'\n                };\n            }\n            // Each agent validates the final solution\n            let validationScore = 0;\n            let validationCount = 0;\n            for (const agentId of agentKeys){\n                const agentKey = state.agentApiKeys[agentId];\n                const validationPrompt = `[SOLUTION VALIDATION]\nYou are ${manager.getState().agentRoles[agentId]} validating the team's final solution.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nFinal Team Solution:\n${finalSolution}\n\nYour task:\n1. **EVALUATE** how well the solution addresses the original request\n2. **CHECK** if it incorporates the team's best ideas and consensus\n3. **ASSESS** the quality, completeness, and effectiveness\n4. **IDENTIFY** any remaining gaps or improvements needed\n5. **RATE** the solution on a scale of 1-10\n\nProvide your validation in this format:\n## Validation Assessment\n[Your detailed assessment of the solution]\n\n## Quality Rating: X/10\n[Explanation of your rating]\n\n## Final Approval: YES/NO\n[Whether you approve this as the final solution]`;\n                try {\n                    const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                        custom_api_config_id: state.customApiConfigId,\n                        messages: [\n                            {\n                                role: 'user',\n                                content: validationPrompt\n                            }\n                        ],\n                        temperature: 0.2,\n                        max_tokens: 2000,\n                        stream: false,\n                        role: 'orchestration'\n                    });\n                    if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                        const response = result.responseData.choices[0].message.content;\n                        // Extract rating\n                        const ratingMatch = response.match(/(?:Quality Rating|Rating):\\s*(\\d+)\\/10/i);\n                        if (ratingMatch) {\n                            validationScore += parseInt(ratingMatch[1]);\n                            validationCount++;\n                        }\n                        manager.addTurn({\n                            agent: agentId,\n                            agentLabel: agentKey.label,\n                            message: response,\n                            messageType: 'agreement'\n                        });\n                        console.log(`[Validation] ✅ ${agentKey.label} completed validation`);\n                    }\n                } catch (error) {\n                    console.error(`[Validation] Error with ${agentKey.label}:`, error);\n                }\n            }\n            // Calculate final quality score\n            const avgValidationScore = validationCount > 0 ? validationScore / validationCount : 0;\n            manager.getState().qualityMetrics.noveltyScore = avgValidationScore / 10;\n            manager.setPhaseProgress('validation', 1.0);\n            manager.updateQualityMetrics();\n            console.log(`[Validation] ✅ Validation complete. Average score: ${avgValidationScore.toFixed(1)}/10`);\n            return {\n                nextAction: 'complete',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2FnZW50LWNvbGxhYm9yYXRpb24vQ29udmVyc2F0aW9uTm9kZXMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsMERBQTBEO0FBQzFELDBFQUEwRTtBQUtHO0FBQ3JDO0FBcUJqQyxNQUFNRTtJQUVYOztHQUVDLEdBQ0QsT0FBT0MsMEJBQTBCO1FBQy9CLE9BQU8sT0FBT0M7WUFDWkMsUUFBUUMsR0FBRyxDQUFDLENBQUMsNENBQTRDLENBQUM7WUFFMUQsTUFBTUMsVUFBVUgsTUFBTUksbUJBQW1CO1lBQ3pDLE1BQU1DLFlBQVlDLE9BQU9DLElBQUksQ0FBQ1AsTUFBTVEsWUFBWTtZQUVoRCx1Q0FBdUM7WUFDdkMsS0FBSyxNQUFNQyxXQUFXSixVQUFXO2dCQUMvQixNQUFNSyxXQUFXVixNQUFNUSxZQUFZLENBQUNDLFFBQVE7Z0JBQzVDLE1BQU1FLGlCQUFpQlIsUUFBUVMsc0JBQXNCO2dCQUVyRCxNQUFNQyxtQkFBbUIsR0FBR0YsZUFBZTs7O1FBRzNDLEVBQUVSLFFBQVFXLFFBQVEsR0FBR0MsVUFBVSxDQUFDTixRQUFRLENBQUM7O21CQUU5QixFQUFFTixRQUFRVyxRQUFRLEdBQUdFLFVBQVUsQ0FBQzs7Ozs7Ozs7O0FBU25ELEVBQUViLFFBQVFjLGNBQWMsQ0FBQyxHQUFHQyxNQUFNLEdBQUcsSUFBSSx3RUFBd0UsOEVBQThFOzswR0FFckYsQ0FBQztnQkFFbkcsSUFBSTtvQkFDRixNQUFNQyxTQUFTLE1BQU12Qix5RkFBc0JBLENBQ3pDYyxTQUFTVSxRQUFRLEVBQ2pCVixTQUFTVyxtQkFBbUIsRUFDNUIsTUFBTXhCLG9EQUFPQSxDQUFDYSxTQUFTWSxpQkFBaUIsR0FDeEM7d0JBQ0VDLHNCQUFzQnZCLE1BQU13QixpQkFBaUI7d0JBQzdDQyxVQUFVOzRCQUFDO2dDQUFFQyxNQUFNO2dDQUFRQyxTQUFTZDs0QkFBaUI7eUJBQUU7d0JBQ3ZEZSxhQUFhO3dCQUNiQyxZQUFZO3dCQUNaQyxRQUFRO3dCQUNSSixNQUFNO29CQUNSO29CQUdGLElBQUlQLE9BQU9ZLE9BQU8sSUFBSVosT0FBT2EsWUFBWSxFQUFFQyxTQUFTLENBQUMsRUFBRSxFQUFFQyxTQUFTUCxTQUFTO3dCQUN6RSxNQUFNUSxXQUFXaEIsT0FBT2EsWUFBWSxDQUFDQyxPQUFPLENBQUMsRUFBRSxDQUFDQyxPQUFPLENBQUNQLE9BQU87d0JBRS9ELHNCQUFzQjt3QkFDdEJ4QixRQUFRaUMsT0FBTyxDQUFDOzRCQUNkQyxPQUFPNUI7NEJBQ1A2QixZQUFZNUIsU0FBUzZCLEtBQUs7NEJBQzFCTCxTQUFTQzs0QkFDVEssYUFBYTs0QkFDYkMsY0FBY3RDLFFBQVFjLGNBQWMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFeUIsR0FBRyxtQ0FBbUM7d0JBQ3BGO3dCQUVBekMsUUFBUUMsR0FBRyxDQUFDLENBQUMsa0JBQWtCLEVBQUVRLFNBQVM2QixLQUFLLENBQUMsb0JBQW9CLEVBQUVKLFNBQVNqQixNQUFNLENBQUMsT0FBTyxDQUFDO29CQUNoRyxPQUFPO3dCQUNMakIsUUFBUTBDLEtBQUssQ0FBQyxDQUFDLGtCQUFrQixFQUFFakMsU0FBUzZCLEtBQUssQ0FBQyxxQkFBcUIsQ0FBQztvQkFDMUU7Z0JBQ0YsRUFBRSxPQUFPSSxPQUFPO29CQUNkMUMsUUFBUTBDLEtBQUssQ0FBQyxDQUFDLDJCQUEyQixFQUFFakMsU0FBUzZCLEtBQUssQ0FBQyxDQUFDLENBQUMsRUFBRUk7Z0JBQ2pFO1lBQ0Y7WUFFQSx3QkFBd0I7WUFDeEJ4QyxRQUFReUMsZ0JBQWdCLENBQUMsaUJBQWlCO1lBQzFDekMsUUFBUTBDLG9CQUFvQjtZQUU1QixPQUFPO2dCQUNMQyxZQUFZO2dCQUNaQyxnQkFBZ0IvQyxNQUFNK0MsY0FBYyxHQUFHO1lBQ3pDO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsT0FBT0MsbUJBQW1CO1FBQ3hCLE9BQU8sT0FBT2hEO1lBQ1pDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDhCQUE4QixDQUFDO1lBRTVDLE1BQU1DLFVBQVVILE1BQU1JLG1CQUFtQjtZQUN6QyxNQUFNQyxZQUFZQyxPQUFPQyxJQUFJLENBQUNQLE1BQU1RLFlBQVk7WUFFaEQsa0ZBQWtGO1lBQ2xGUCxRQUFRQyxHQUFHLENBQUMsQ0FBQyx3QkFBd0IsRUFBRUMsUUFBUThDLGVBQWUsSUFBSTtZQUVsRSw0Q0FBNEM7WUFDNUMsS0FBSyxNQUFNeEMsV0FBV0osVUFBVztnQkFDL0IsTUFBTUssV0FBV1YsTUFBTVEsWUFBWSxDQUFDQyxRQUFRO2dCQUM1QyxNQUFNRSxpQkFBaUJSLFFBQVFTLHNCQUFzQjtnQkFDckQsTUFBTXNDLGNBQWMvQyxRQUFRYyxjQUFjLENBQUM7Z0JBRTNDLE1BQU1rQyxlQUFlLEdBQUd4QyxlQUFlOzs7UUFHdkMsRUFBRVIsUUFBUVcsUUFBUSxHQUFHQyxVQUFVLENBQUNOLFFBQVEsQ0FBQzs7bUJBRTlCLEVBQUVOLFFBQVFXLFFBQVEsR0FBR0UsVUFBVSxDQUFDOzs7QUFHbkQsRUFBRWtDLFlBQVlFLEdBQUcsQ0FBQ0MsQ0FBQUEsT0FBUSxHQUFHQSxLQUFLZixVQUFVLENBQUMsRUFBRSxFQUFFZSxLQUFLbkIsT0FBTyxFQUFFLEVBQUVvQixJQUFJLENBQUMsUUFBUTs7Ozs7Ozs7OzswSkFVNEUsQ0FBQztnQkFFbkosSUFBSTtvQkFDRixNQUFNbkMsU0FBUyxNQUFNdkIseUZBQXNCQSxDQUN6Q2MsU0FBU1UsUUFBUSxFQUNqQlYsU0FBU1csbUJBQW1CLEVBQzVCLE1BQU14QixvREFBT0EsQ0FBQ2EsU0FBU1ksaUJBQWlCLEdBQ3hDO3dCQUNFQyxzQkFBc0J2QixNQUFNd0IsaUJBQWlCO3dCQUM3Q0MsVUFBVTs0QkFBQztnQ0FBRUMsTUFBTTtnQ0FBUUMsU0FBU3dCOzRCQUFhO3lCQUFFO3dCQUNuRHZCLGFBQWE7d0JBQ2JDLFlBQVk7d0JBQ1pDLFFBQVE7d0JBQ1JKLE1BQU07b0JBQ1I7b0JBR0YsSUFBSVAsT0FBT1ksT0FBTyxJQUFJWixPQUFPYSxZQUFZLEVBQUVDLFNBQVMsQ0FBQyxFQUFFLEVBQUVDLFNBQVNQLFNBQVM7d0JBQ3pFLE1BQU1RLFdBQVdoQixPQUFPYSxZQUFZLENBQUNDLE9BQU8sQ0FBQyxFQUFFLENBQUNDLE9BQU8sQ0FBQ1AsT0FBTzt3QkFFL0QsNENBQTRDO3dCQUM1QyxNQUFNNEIsa0JBQWtCcEIsU0FBU3FCLFdBQVcsR0FBR0MsUUFBUSxDQUFDLGVBQ2xDdEIsU0FBU3FCLFdBQVcsR0FBR0MsUUFBUSxDQUFDLGNBQ2hDdEIsU0FBU3FCLFdBQVcsR0FBR0MsUUFBUSxDQUFDO3dCQUV0RCxJQUFJRixpQkFBaUI7NEJBQ25CLDhEQUE4RDs0QkFDOUQsTUFBTUcsYUFBYXZELFFBQVF3RCxrQkFBa0I7NEJBQzdDLElBQUlELFdBQVdFLE9BQU8sR0FBR0YsV0FBV0csR0FBRyxFQUFFO2dDQUN2QyxxREFBcUQ7Z0NBQ3JELE1BQU1DLGFBQWEzQixTQUFTNEIsS0FBSyxDQUFDO2dDQUNsQyxJQUFJRCxjQUFjQSxVQUFVLENBQUMsRUFBRSxDQUFDNUMsTUFBTSxHQUFHLElBQUk7b0NBQzNDLE1BQU04QyxpQkFBaUI3RCxRQUFROEQsbUJBQW1CO29DQUNsRCxNQUFNQyxjQUFjRixlQUFlRyxJQUFJLENBQUNDLENBQUFBLFFBQ3RDQSxNQUFNQyxXQUFXLENBQUNiLFdBQVcsR0FBR0MsUUFBUSxDQUFDSyxVQUFVLENBQUMsRUFBRSxDQUFDTixXQUFXLEdBQUdjLFNBQVMsQ0FBQyxHQUFHO29DQUdwRixJQUFJLENBQUNKLGFBQWE7d0NBQ2hCL0QsUUFBUW9FLGtCQUFrQixDQUN4QlQsVUFBVSxDQUFDLEVBQUUsRUFDYjs0Q0FBQ3JEO3lDQUFRLEVBQ1Q7b0NBRUo7Z0NBQ0Y7NEJBQ0YsT0FBTztnQ0FDTFIsUUFBUUMsR0FBRyxDQUFDLENBQUMsNERBQTRELENBQUM7NEJBQzVFO3dCQUNGO3dCQUVBQyxRQUFRaUMsT0FBTyxDQUFDOzRCQUNkQyxPQUFPNUI7NEJBQ1A2QixZQUFZNUIsU0FBUzZCLEtBQUs7NEJBQzFCTCxTQUFTQzs0QkFDVEssYUFBYWUsa0JBQWtCLGFBQWE7NEJBQzVDZCxjQUFjUyxXQUFXLENBQUNBLFlBQVloQyxNQUFNLEdBQUcsRUFBRSxFQUFFd0I7d0JBQ3JEO3dCQUVBekMsUUFBUUMsR0FBRyxDQUFDLENBQUMsV0FBVyxFQUFFUSxTQUFTNkIsS0FBSyxDQUFDLDZCQUE2QixFQUFFSixTQUFTakIsTUFBTSxDQUFDLE9BQU8sQ0FBQztvQkFDbEc7Z0JBQ0YsRUFBRSxPQUFPeUIsT0FBTztvQkFDZDFDLFFBQVEwQyxLQUFLLENBQUMsQ0FBQyxvQkFBb0IsRUFBRWpDLFNBQVM2QixLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUVJO2dCQUMxRDtZQUNGO1lBRUF4QyxRQUFReUMsZ0JBQWdCLENBQUMsVUFBVTtZQUNuQ3pDLFFBQVEwQyxvQkFBb0I7WUFFNUIsb0VBQW9FO1lBQ3BFLE1BQU0yQixtQkFBbUJyRSxRQUFROEQsbUJBQW1CO1lBQ3BELElBQUlPLGlCQUFpQnRELE1BQU0sR0FBRyxLQUFLbEIsTUFBTStDLGNBQWMsR0FBRy9DLE1BQU15RSxhQUFhLEVBQUU7Z0JBQzdFLE9BQU87b0JBQ0wzQixZQUFZO29CQUNaQyxnQkFBZ0IvQyxNQUFNK0MsY0FBYyxHQUFHO2dCQUN6QztZQUNGO1lBRUEsT0FBTztnQkFDTEQsWUFBWTtnQkFDWkMsZ0JBQWdCL0MsTUFBTStDLGNBQWMsR0FBRztZQUN6QztRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE9BQU8yQixzQkFBc0I7UUFDM0IsT0FBTyxPQUFPMUU7WUFDWkMsUUFBUUMsR0FBRyxDQUFDLENBQUMsb0NBQW9DLENBQUM7WUFFbEQsTUFBTUMsVUFBVUgsTUFBTUksbUJBQW1CO1lBQ3pDLE1BQU1DLFlBQVlDLE9BQU9DLElBQUksQ0FBQ1AsTUFBTVEsWUFBWTtZQUVoRCw4REFBOEQ7WUFDOURQLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJCQUEyQixFQUFFQyxRQUFROEMsZUFBZSxJQUFJO1lBRXJFLDZDQUE2QztZQUM3QyxNQUFNdUIsbUJBQW1CckUsUUFBUThELG1CQUFtQjtZQUNwRCxNQUFNVSw0QkFBNEJ4RSxRQUFRd0UseUJBQXlCO1lBRW5FLElBQUlILGlCQUFpQnRELE1BQU0sR0FBRyxHQUFHO2dCQUMvQixJQUFJeUQsMkJBQTJCO29CQUM3QjFFLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdFQUFnRSxFQUFFc0UsaUJBQWlCdEQsTUFBTSxDQUFDLGlCQUFpQixDQUFDO29CQUN6SCwyQ0FBMkM7b0JBQzNDLEtBQUssTUFBTWtELFNBQVNJLGlCQUFrQjt3QkFDcENyRSxRQUFReUUsWUFBWSxDQUFDUixNQUFNMUIsRUFBRSxFQUFFO29CQUNqQztnQkFDRixPQUFPO29CQUNMekMsUUFBUUMsR0FBRyxDQUFDLENBQUMsc0JBQXNCLEVBQUVzRSxpQkFBaUJ0RCxNQUFNLENBQUMsd0JBQXdCLENBQUM7Z0JBQ3hGO2dCQUVBLG1FQUFtRTtnQkFDbkUsSUFBSSxDQUFDeUQsNkJBQTZCSCxpQkFBaUJ0RCxNQUFNLEdBQUcsR0FBRztvQkFDN0QsS0FBSyxNQUFNa0QsU0FBU0ksaUJBQWlCSyxLQUFLLENBQUMsR0FBRyxHQUFJO3dCQUNsRCxLQUFLLE1BQU1wRSxXQUFXSixVQUFXOzRCQUMvQixNQUFNSyxXQUFXVixNQUFNUSxZQUFZLENBQUNDLFFBQVE7NEJBRTVDLE1BQU1xRSxtQkFBbUIsQ0FBQztRQUM5QixFQUFFM0UsUUFBUVcsUUFBUSxHQUFHQyxVQUFVLENBQUNOLFFBQVEsQ0FBQzs7bUJBRTlCLEVBQUVOLFFBQVFXLFFBQVEsR0FBR0UsVUFBVSxDQUFDOzttQkFFaEMsRUFBRW9ELE1BQU1DLFdBQVcsQ0FBQzs7U0FFOUIsRUFBRWxFLFFBQVFTLHNCQUFzQixHQUFHOzs7Ozs7Ozs2REFRaUIsQ0FBQzs0QkFFbEQsSUFBSTtnQ0FDRixNQUFNTyxTQUFTLE1BQU12Qix5RkFBc0JBLENBQ3pDYyxTQUFTVSxRQUFRLEVBQ2pCVixTQUFTVyxtQkFBbUIsRUFDNUIsTUFBTXhCLG9EQUFPQSxDQUFDYSxTQUFTWSxpQkFBaUIsR0FDeEM7b0NBQ0VDLHNCQUFzQnZCLE1BQU13QixpQkFBaUI7b0NBQzdDQyxVQUFVO3dDQUFDOzRDQUFFQyxNQUFNOzRDQUFRQyxTQUFTbUQ7d0NBQWlCO3FDQUFFO29DQUN2RGxELGFBQWE7b0NBQ2JDLFlBQVk7b0NBQ1pDLFFBQVE7b0NBQ1JKLE1BQU07Z0NBQ1I7Z0NBR0YsSUFBSVAsT0FBT1ksT0FBTyxJQUFJWixPQUFPYSxZQUFZLEVBQUVDLFNBQVMsQ0FBQyxFQUFFLEVBQUVDLFNBQVNQLFNBQVM7b0NBQ3pFLE1BQU1RLFdBQVdoQixPQUFPYSxZQUFZLENBQUNDLE9BQU8sQ0FBQyxFQUFFLENBQUNDLE9BQU8sQ0FBQ1AsT0FBTztvQ0FFL0R4QixRQUFRaUMsT0FBTyxDQUFDO3dDQUNkQyxPQUFPNUI7d0NBQ1A2QixZQUFZNUIsU0FBUzZCLEtBQUs7d0NBQzFCTCxTQUFTQzt3Q0FDVEssYUFBYTt3Q0FDYkMsY0FBY3NDO29DQUNoQjtvQ0FFQSxtQ0FBbUM7b0NBQ25DLElBQUk1QyxTQUFTcUIsV0FBVyxHQUFHQyxRQUFRLENBQUMsWUFBWXRCLFNBQVNxQixXQUFXLEdBQUdDLFFBQVEsQ0FBQyxhQUFhO3dDQUMzRnRELFFBQVF5RSxZQUFZLENBQUNSLE1BQU0xQixFQUFFLEVBQUVQO29DQUNqQztnQ0FDRjs0QkFDRixFQUFFLE9BQU9RLE9BQU87Z0NBQ2QxQyxRQUFRMEMsS0FBSyxDQUFDLENBQUMsdUNBQXVDLEVBQUVqQyxTQUFTNkIsS0FBSyxDQUFDLENBQUMsQ0FBQyxFQUFFSTs0QkFDN0U7d0JBQ0Y7b0JBQ0Y7Z0JBQ0EsRUFBRSwrQ0FBK0M7WUFDbkQ7WUFFQSw2Q0FBNkM7WUFDN0MxQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxpREFBaUQsQ0FBQztZQUUvRCxNQUFNOEUsWUFBWTNFLFNBQVMsQ0FBQyxFQUFFLEVBQUUsMkNBQTJDO1lBQzNFLE1BQU00RSxlQUFlakYsTUFBTVEsWUFBWSxDQUFDd0UsVUFBVTtZQUVsRCxNQUFNRSxrQkFBa0IsQ0FBQztRQUN2QixFQUFFL0UsUUFBUVcsUUFBUSxHQUFHQyxVQUFVLENBQUNpRSxVQUFVLENBQUM7O21CQUVoQyxFQUFFN0UsUUFBUVcsUUFBUSxHQUFHRSxVQUFVLENBQUM7OztBQUduRCxFQUFFYixRQUFRVyxRQUFRLEdBQUdxRSxtQkFBbUIsQ0FBQy9CLEdBQUcsQ0FBQ0MsQ0FBQUEsT0FDM0MsR0FBR0EsS0FBS2YsVUFBVSxDQUFDLEVBQUUsRUFBRWUsS0FBS2IsV0FBVyxDQUFDLEdBQUcsRUFBRWEsS0FBS25CLE9BQU8sRUFBRSxFQUMzRG9CLElBQUksQ0FBQyxRQUFROzs7QUFHZixFQUFFbkQsUUFBUWlGLGlCQUFpQixHQUFHaEMsR0FBRyxDQUFDaUMsQ0FBQUEsT0FBUSxDQUFDLEVBQUUsRUFBRUEsS0FBS0MsS0FBSyxDQUFDLEVBQUUsRUFBRUQsS0FBS0UsV0FBVyxFQUFFLEVBQUVqQyxJQUFJLENBQUMsTUFBTTs7Ozs7Ozs7OzZGQVNBLENBQUM7WUFFeEYsSUFBSTtnQkFDRixNQUFNbkMsU0FBUyxNQUFNdkIseUZBQXNCQSxDQUN6Q3FGLGFBQWE3RCxRQUFRLEVBQ3JCNkQsYUFBYTVELG1CQUFtQixFQUNoQyxNQUFNeEIsb0RBQU9BLENBQUNvRixhQUFhM0QsaUJBQWlCLEdBQzVDO29CQUNFQyxzQkFBc0J2QixNQUFNd0IsaUJBQWlCO29CQUM3Q0MsVUFBVTt3QkFBQzs0QkFBRUMsTUFBTTs0QkFBUUMsU0FBU3VEO3dCQUFnQjtxQkFBRTtvQkFDdER0RCxhQUFhO29CQUNiQyxZQUFZO29CQUNaQyxRQUFRO29CQUNSSixNQUFNO2dCQUNSO2dCQUdGLElBQUlQLE9BQU9ZLE9BQU8sSUFBSVosT0FBT2EsWUFBWSxFQUFFQyxTQUFTLENBQUMsRUFBRSxFQUFFQyxTQUFTUCxTQUFTO29CQUN6RSxNQUFNNkQsZ0JBQWdCckUsT0FBT2EsWUFBWSxDQUFDQyxPQUFPLENBQUMsRUFBRSxDQUFDQyxPQUFPLENBQUNQLE9BQU87b0JBRXBFMUIsUUFBUUMsR0FBRyxDQUFDLENBQUMsdUNBQXVDLEVBQUVzRixjQUFjdEUsTUFBTSxDQUFDLE9BQU8sQ0FBQztvQkFDbkZmLFFBQVFzRixnQkFBZ0IsQ0FBQ0Q7b0JBQ3pCckYsUUFBUWlDLE9BQU8sQ0FBQzt3QkFDZEMsT0FBTzJDO3dCQUNQMUMsWUFBWTJDLGFBQWExQyxLQUFLO3dCQUM5QkwsU0FBU3NEO3dCQUNUaEQsYUFBYTtvQkFDZjtvQkFFQXZDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdFQUFnRSxDQUFDO2dCQUNoRixPQUFPO29CQUNMRCxRQUFRMEMsS0FBSyxDQUFDLENBQUMsMkRBQTJELENBQUMsRUFBRXhCLE9BQU93QixLQUFLO2dCQUMzRjtZQUNGLEVBQUUsT0FBT0EsT0FBTztnQkFDZDFDLFFBQVEwQyxLQUFLLENBQUMsQ0FBQyw0Q0FBNEMsQ0FBQyxFQUFFQTtZQUNoRTtZQUVBeEMsUUFBUXlDLGdCQUFnQixDQUFDLGFBQWE7WUFDdEN6QyxRQUFRMEMsb0JBQW9CO1lBRTVCLE9BQU87Z0JBQ0xDLFlBQVk7Z0JBQ1pDLGdCQUFnQi9DLE1BQU0rQyxjQUFjLEdBQUc7WUFDekM7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxPQUFPMkMsdUJBQXVCO1FBQzVCLE9BQU8sT0FBTzFGO1lBQ1pDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNDQUFzQyxDQUFDO1lBRXBELE1BQU1DLFVBQVVILE1BQU1JLG1CQUFtQjtZQUN6QyxNQUFNQyxZQUFZQyxPQUFPQyxJQUFJLENBQUNQLE1BQU1RLFlBQVk7WUFFaEQsOERBQThEO1lBQzlEUCxRQUFRQyxHQUFHLENBQUMsQ0FBQyw0QkFBNEIsRUFBRUMsUUFBUThDLGVBQWUsSUFBSTtZQUV0RSxNQUFNdUMsZ0JBQWdCckYsUUFBUVcsUUFBUSxHQUFHMEUsYUFBYTtZQUN0RHZGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNEQUFzRCxFQUFFc0YsZ0JBQWdCLFFBQVEsTUFBTTtZQUNuRyxJQUFJLENBQUNBLGVBQWU7Z0JBQ2xCdkYsUUFBUTBDLEtBQUssQ0FBQyxDQUFDLHdFQUF3RSxDQUFDO2dCQUN4RjFDLFFBQVEwQyxLQUFLLENBQUMsQ0FBQyxxQ0FBcUMsRUFBRXhDLFFBQVE4QyxlQUFlLEdBQUcsVUFBVSxFQUFFOUMsUUFBUVcsUUFBUSxHQUFHcUUsbUJBQW1CLENBQUNqRSxNQUFNLENBQUMsTUFBTSxDQUFDO2dCQUNqSixPQUFPO29CQUFFNEIsWUFBWTtnQkFBVztZQUNsQztZQUVBLDBDQUEwQztZQUMxQyxJQUFJNkMsa0JBQWtCO1lBQ3RCLElBQUlDLGtCQUFrQjtZQUV0QixLQUFLLE1BQU1uRixXQUFXSixVQUFXO2dCQUMvQixNQUFNSyxXQUFXVixNQUFNUSxZQUFZLENBQUNDLFFBQVE7Z0JBRTVDLE1BQU1vRixtQkFBbUIsQ0FBQztRQUMxQixFQUFFMUYsUUFBUVcsUUFBUSxHQUFHQyxVQUFVLENBQUNOLFFBQVEsQ0FBQzs7bUJBRTlCLEVBQUVOLFFBQVFXLFFBQVEsR0FBR0UsVUFBVSxDQUFDOzs7QUFHbkQsRUFBRXdFLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7O2dEQWlCZ0MsQ0FBQztnQkFFekMsSUFBSTtvQkFDRixNQUFNckUsU0FBUyxNQUFNdkIseUZBQXNCQSxDQUN6Q2MsU0FBU1UsUUFBUSxFQUNqQlYsU0FBU1csbUJBQW1CLEVBQzVCLE1BQU14QixvREFBT0EsQ0FBQ2EsU0FBU1ksaUJBQWlCLEdBQ3hDO3dCQUNFQyxzQkFBc0J2QixNQUFNd0IsaUJBQWlCO3dCQUM3Q0MsVUFBVTs0QkFBQztnQ0FBRUMsTUFBTTtnQ0FBUUMsU0FBU2tFOzRCQUFpQjt5QkFBRTt3QkFDdkRqRSxhQUFhO3dCQUNiQyxZQUFZO3dCQUNaQyxRQUFRO3dCQUNSSixNQUFNO29CQUNSO29CQUdGLElBQUlQLE9BQU9ZLE9BQU8sSUFBSVosT0FBT2EsWUFBWSxFQUFFQyxTQUFTLENBQUMsRUFBRSxFQUFFQyxTQUFTUCxTQUFTO3dCQUN6RSxNQUFNUSxXQUFXaEIsT0FBT2EsWUFBWSxDQUFDQyxPQUFPLENBQUMsRUFBRSxDQUFDQyxPQUFPLENBQUNQLE9BQU87d0JBRS9ELGlCQUFpQjt3QkFDakIsTUFBTW1FLGNBQWMzRCxTQUFTNEIsS0FBSyxDQUFDO3dCQUNuQyxJQUFJK0IsYUFBYTs0QkFDZkgsbUJBQW1CSSxTQUFTRCxXQUFXLENBQUMsRUFBRTs0QkFDMUNGO3dCQUNGO3dCQUVBekYsUUFBUWlDLE9BQU8sQ0FBQzs0QkFDZEMsT0FBTzVCOzRCQUNQNkIsWUFBWTVCLFNBQVM2QixLQUFLOzRCQUMxQkwsU0FBU0M7NEJBQ1RLLGFBQWE7d0JBQ2Y7d0JBRUF2QyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxlQUFlLEVBQUVRLFNBQVM2QixLQUFLLENBQUMscUJBQXFCLENBQUM7b0JBQ3JFO2dCQUNGLEVBQUUsT0FBT0ksT0FBTztvQkFDZDFDLFFBQVEwQyxLQUFLLENBQUMsQ0FBQyx3QkFBd0IsRUFBRWpDLFNBQVM2QixLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUVJO2dCQUM5RDtZQUNGO1lBRUEsZ0NBQWdDO1lBQ2hDLE1BQU1xRCxxQkFBcUJKLGtCQUFrQixJQUFJRCxrQkFBa0JDLGtCQUFrQjtZQUNyRnpGLFFBQVFXLFFBQVEsR0FBR21GLGNBQWMsQ0FBQ0MsWUFBWSxHQUFHRixxQkFBcUI7WUFFdEU3RixRQUFReUMsZ0JBQWdCLENBQUMsY0FBYztZQUN2Q3pDLFFBQVEwQyxvQkFBb0I7WUFFNUI1QyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxtREFBbUQsRUFBRThGLG1CQUFtQkcsT0FBTyxDQUFDLEdBQUcsR0FBRyxDQUFDO1lBRXBHLE9BQU87Z0JBQ0xyRCxZQUFZO2dCQUNaQyxnQkFBZ0IvQyxNQUFNK0MsY0FBYyxHQUFHO1lBQ3pDO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcbGliXFxhZ2VudC1jb2xsYWJvcmF0aW9uXFxDb252ZXJzYXRpb25Ob2Rlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUcnVlIEFnZW50IENvbGxhYm9yYXRpb24gLSBMYW5nR3JhcGggQ29udmVyc2F0aW9uIE5vZGVzXG4vLyBUaGVzZSBub2RlcyBoYW5kbGUgZGlmZmVyZW50IHBoYXNlcyBvZiByZWFsIGFnZW50LXRvLWFnZW50IGNvbnZlcnNhdGlvblxuXG5pbXBvcnQgeyBTdGF0ZUdyYXBoLCBTVEFSVCwgRU5EIH0gZnJvbSAnQGxhbmdjaGFpbi9sYW5nZ3JhcGgnO1xuaW1wb3J0IHsgQmFzZU1lc3NhZ2UsIEh1bWFuTWVzc2FnZSwgQUlNZXNzYWdlIH0gZnJvbSAnQGxhbmdjaGFpbi9jb3JlL21lc3NhZ2VzJztcbmltcG9ydCB7IENvbnZlcnNhdGlvblN0YXRlTWFuYWdlciwgQ29udmVyc2F0aW9uVHVybiwgQ29sbGFib3JhdGlvblN0YXRlIH0gZnJvbSAnLi9Db252ZXJzYXRpb25TdGF0ZSc7XG5pbXBvcnQgeyBleGVjdXRlUHJvdmlkZXJSZXF1ZXN0IH0gZnJvbSAnLi4vcHJvdmlkZXJzL2V4ZWN1dGVQcm92aWRlclJlcXVlc3QnO1xuaW1wb3J0IHsgZGVjcnlwdCB9IGZyb20gJy4uL2VuY3J5cHRpb24nO1xuXG5leHBvcnQgaW50ZXJmYWNlIEFwaUtleSB7XG4gIGlkOiBzdHJpbmc7XG4gIHByb3ZpZGVyOiBzdHJpbmc7XG4gIHByZWRlZmluZWRfbW9kZWxfaWQ6IHN0cmluZztcbiAgZW5jcnlwdGVkX2FwaV9rZXk6IHN0cmluZztcbiAgbGFiZWw6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDb252ZXJzYXRpb25HcmFwaFN0YXRlIHtcbiAgbWVzc2FnZXM6IEJhc2VNZXNzYWdlW107XG4gIGNvbnZlcnNhdGlvbk1hbmFnZXI6IENvbnZlcnNhdGlvblN0YXRlTWFuYWdlcjtcbiAgYWdlbnRBcGlLZXlzOiBSZWNvcmQ8c3RyaW5nLCBBcGlLZXk+O1xuICBjdXN0b21BcGlDb25maWdJZDogc3RyaW5nO1xuICBjdXJyZW50QWdlbnQ/OiBzdHJpbmc7XG4gIG5leHRBY3Rpb24/OiAnY29udGludWVfZGlzY3Vzc2lvbicgfCAnYWR2YW5jZV9waGFzZScgfCAnY29tcGxldGUnO1xuICBpdGVyYXRpb25Db3VudDogbnVtYmVyO1xuICBtYXhJdGVyYXRpb25zOiBudW1iZXI7XG59XG5cbmV4cG9ydCBjbGFzcyBDb252ZXJzYXRpb25Ob2RlcyB7XG4gIFxuICAvKipcbiAgICogQnJhaW5zdG9ybWluZyBQaGFzZSBOb2RlIC0gQWdlbnRzIHNoYXJlIGluaXRpYWwgaWRlYXMgYW5kIGV4cGxvcmUgdGhlIHByb2JsZW0gc3BhY2VcbiAgICovXG4gIHN0YXRpYyBjcmVhdGVCcmFpbnN0b3JtaW5nTm9kZSgpIHtcbiAgICByZXR1cm4gYXN5bmMgKHN0YXRlOiBDb252ZXJzYXRpb25HcmFwaFN0YXRlKTogUHJvbWlzZTxQYXJ0aWFsPENvbnZlcnNhdGlvbkdyYXBoU3RhdGU+PiA9PiB7XG4gICAgICBjb25zb2xlLmxvZyhgW0JyYWluc3Rvcm1pbmddIFN0YXJ0aW5nIGJyYWluc3Rvcm1pbmcgcGhhc2VgKTtcbiAgICAgIFxuICAgICAgY29uc3QgbWFuYWdlciA9IHN0YXRlLmNvbnZlcnNhdGlvbk1hbmFnZXI7XG4gICAgICBjb25zdCBhZ2VudEtleXMgPSBPYmplY3Qua2V5cyhzdGF0ZS5hZ2VudEFwaUtleXMpO1xuICAgICAgXG4gICAgICAvLyBFYWNoIGFnZW50IGNvbnRyaWJ1dGVzIGluaXRpYWwgaWRlYXNcbiAgICAgIGZvciAoY29uc3QgYWdlbnRJZCBvZiBhZ2VudEtleXMpIHtcbiAgICAgICAgY29uc3QgYWdlbnRLZXkgPSBzdGF0ZS5hZ2VudEFwaUtleXNbYWdlbnRJZF07XG4gICAgICAgIGNvbnN0IGNvbnRleHRTdW1tYXJ5ID0gbWFuYWdlci5nZW5lcmF0ZUNvbnRleHRTdW1tYXJ5KCk7XG4gICAgICAgIFxuICAgICAgICBjb25zdCBicmFpbnN0b3JtUHJvbXB0ID0gYCR7Y29udGV4dFN1bW1hcnl9XG5cbltCUkFJTlNUT1JNSU5HIFBIQVNFIC0gSU5JVElBTCBJREVBU11cbllvdSBhcmUgJHttYW5hZ2VyLmdldFN0YXRlKCkuYWdlbnRSb2xlc1thZ2VudElkXX0gcGFydGljaXBhdGluZyBpbiBhIGNvbGxhYm9yYXRpdmUgYnJhaW5zdG9ybWluZyBzZXNzaW9uLlxuXG5PcmlnaW5hbCBSZXF1ZXN0OiBcIiR7bWFuYWdlci5nZXRTdGF0ZSgpLnVzZXJQcm9tcHR9XCJcblxuWW91ciB0YXNrIGluIHRoaXMgYnJhaW5zdG9ybWluZyBwaGFzZTpcbjEuICoqQU5BTFlaRSoqIHRoZSByZXF1ZXN0IGFuZCBzaGFyZSB5b3VyIGluaXRpYWwgdW5kZXJzdGFuZGluZ1xuMi4gKipQUk9QT1NFKiogY3JlYXRpdmUgYXBwcm9hY2hlcyBhbmQgaWRlYXNcbjMuICoqSURFTlRJRlkqKiBrZXkgY2hhbGxlbmdlcyBhbmQgb3Bwb3J0dW5pdGllc1xuNC4gKipBU0sgUVVFU1RJT05TKiogdGhhdCB3aWxsIGhlbHAgdGhlIHRlYW0gdW5kZXJzdGFuZCB0aGUgcHJvYmxlbSBiZXR0ZXJcbjUuICoqQlVJTEQqKiBvbiBhbnkgaWRlYXMgYWxyZWFkeSBzaGFyZWQgYnkgb3RoZXIgYWdlbnRzXG5cbiR7bWFuYWdlci5nZXRSZWNlbnRUdXJucygzKS5sZW5ndGggPiAwID8gJ1Jlc3BvbmQgdG8gYW5kIGJ1aWxkIG9uIHRoZSBpZGVhcyBhbHJlYWR5IHNoYXJlZCBieSB5b3VyIHRlYW1tYXRlcy4nIDogJ1lvdSBhcmUgc3RhcnRpbmcgdGhlIGJyYWluc3Rvcm1pbmcgLSBzaGFyZSB5b3VyIGluaXRpYWwgdGhvdWdodHMgYW5kIGlkZWFzLid9XG5cbkZvY3VzIG9uIGdlbmVyYXRpbmcgZGl2ZXJzZSwgY3JlYXRpdmUgaWRlYXMgcmF0aGVyIHRoYW4gZGV0YWlsZWQgc29sdXRpb25zLiBCZSBjb2xsYWJvcmF0aXZlIGFuZCBlbmdhZ2luZyFgO1xuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZXhlY3V0ZVByb3ZpZGVyUmVxdWVzdChcbiAgICAgICAgICAgIGFnZW50S2V5LnByb3ZpZGVyLFxuICAgICAgICAgICAgYWdlbnRLZXkucHJlZGVmaW5lZF9tb2RlbF9pZCxcbiAgICAgICAgICAgIGF3YWl0IGRlY3J5cHQoYWdlbnRLZXkuZW5jcnlwdGVkX2FwaV9rZXkpLFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICBjdXN0b21fYXBpX2NvbmZpZ19pZDogc3RhdGUuY3VzdG9tQXBpQ29uZmlnSWQsXG4gICAgICAgICAgICAgIG1lc3NhZ2VzOiBbeyByb2xlOiAndXNlcicsIGNvbnRlbnQ6IGJyYWluc3Rvcm1Qcm9tcHQgfV0sXG4gICAgICAgICAgICAgIHRlbXBlcmF0dXJlOiAwLjgsIC8vIEhpZ2ggY3JlYXRpdml0eSBmb3IgYnJhaW5zdG9ybWluZ1xuICAgICAgICAgICAgICBtYXhfdG9rZW5zOiAyMDAwLFxuICAgICAgICAgICAgICBzdHJlYW06IGZhbHNlLFxuICAgICAgICAgICAgICByb2xlOiAnb3JjaGVzdHJhdGlvbidcbiAgICAgICAgICAgIH1cbiAgICAgICAgICApO1xuXG4gICAgICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzICYmIHJlc3VsdC5yZXNwb25zZURhdGE/LmNob2ljZXM/LlswXT8ubWVzc2FnZT8uY29udGVudCkge1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSByZXN1bHQucmVzcG9uc2VEYXRhLmNob2ljZXNbMF0ubWVzc2FnZS5jb250ZW50O1xuICAgICAgICAgICAgXG4gICAgICAgICAgICAvLyBBZGQgdG8gY29udmVyc2F0aW9uXG4gICAgICAgICAgICBtYW5hZ2VyLmFkZFR1cm4oe1xuICAgICAgICAgICAgICBhZ2VudDogYWdlbnRJZCxcbiAgICAgICAgICAgICAgYWdlbnRMYWJlbDogYWdlbnRLZXkubGFiZWwsXG4gICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3BvbnNlLFxuICAgICAgICAgICAgICBtZXNzYWdlVHlwZTogJ3Byb3Bvc2FsJyxcbiAgICAgICAgICAgICAgcmVzcG9uZGluZ1RvOiBtYW5hZ2VyLmdldFJlY2VudFR1cm5zKDEpWzBdPy5pZCAvLyBSZXNwb25kIHRvIG1vc3QgcmVjZW50IGlmIGV4aXN0c1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBbQnJhaW5zdG9ybWluZ10g4pyFICR7YWdlbnRLZXkubGFiZWx9IGNvbnRyaWJ1dGVkIGlkZWFzICgke3Jlc3BvbnNlLmxlbmd0aH0gY2hhcnMpYCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYFtCcmFpbnN0b3JtaW5nXSDinYwgJHthZ2VudEtleS5sYWJlbH0gZmFpbGVkIHRvIGNvbnRyaWJ1dGVgKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihgW0JyYWluc3Rvcm1pbmddIEVycm9yIHdpdGggJHthZ2VudEtleS5sYWJlbH06YCwgZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIFVwZGF0ZSBwaGFzZSBwcm9ncmVzc1xuICAgICAgbWFuYWdlci5zZXRQaGFzZVByb2dyZXNzKCdicmFpbnN0b3JtaW5nJywgMS4wKTtcbiAgICAgIG1hbmFnZXIudXBkYXRlUXVhbGl0eU1ldHJpY3MoKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbmV4dEFjdGlvbjogJ2FkdmFuY2VfcGhhc2UnLFxuICAgICAgICBpdGVyYXRpb25Db3VudDogc3RhdGUuaXRlcmF0aW9uQ291bnQgKyAxXG4gICAgICB9O1xuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogRGViYXRlIFBoYXNlIE5vZGUgLSBBZ2VudHMgY2hhbGxlbmdlIGlkZWFzLCBpZGVudGlmeSBjb25mbGljdHMsIGFuZCByZWZpbmUgdGhpbmtpbmdcbiAgICovXG4gIHN0YXRpYyBjcmVhdGVEZWJhdGVOb2RlKCkge1xuICAgIHJldHVybiBhc3luYyAoc3RhdGU6IENvbnZlcnNhdGlvbkdyYXBoU3RhdGUpOiBQcm9taXNlPFBhcnRpYWw8Q29udmVyc2F0aW9uR3JhcGhTdGF0ZT4+ID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKGBbRGViYXRlXSBTdGFydGluZyBkZWJhdGUgcGhhc2VgKTtcbiAgICAgIFxuICAgICAgY29uc3QgbWFuYWdlciA9IHN0YXRlLmNvbnZlcnNhdGlvbk1hbmFnZXI7XG4gICAgICBjb25zdCBhZ2VudEtleXMgPSBPYmplY3Qua2V5cyhzdGF0ZS5hZ2VudEFwaUtleXMpO1xuXG4gICAgICAvLyBFbnN1cmUgd2UncmUgaW4gZGViYXRlIHBoYXNlIChkb24ndCBhZHZhbmNlIGhlcmUsIGxldCBzdXBlcnZpc29yIG1hbmFnZSBwaGFzZXMpXG4gICAgICBjb25zb2xlLmxvZyhgW0RlYmF0ZV0gQ3VycmVudCBwaGFzZTogJHttYW5hZ2VyLmdldEN1cnJlbnRQaGFzZSgpfWApO1xuICAgICAgXG4gICAgICAvLyBFYWNoIGFnZW50IGNyaXRpcXVlcyBhbmQgY2hhbGxlbmdlcyBpZGVhc1xuICAgICAgZm9yIChjb25zdCBhZ2VudElkIG9mIGFnZW50S2V5cykge1xuICAgICAgICBjb25zdCBhZ2VudEtleSA9IHN0YXRlLmFnZW50QXBpS2V5c1thZ2VudElkXTtcbiAgICAgICAgY29uc3QgY29udGV4dFN1bW1hcnkgPSBtYW5hZ2VyLmdlbmVyYXRlQ29udGV4dFN1bW1hcnkoKTtcbiAgICAgICAgY29uc3QgcmVjZW50VHVybnMgPSBtYW5hZ2VyLmdldFJlY2VudFR1cm5zKDUpO1xuICAgICAgICBcbiAgICAgICAgY29uc3QgZGViYXRlUHJvbXB0ID0gYCR7Y29udGV4dFN1bW1hcnl9XG5cbltERUJBVEUgUEhBU0UgLSBDUklUSUNBTCBBTkFMWVNJU11cbllvdSBhcmUgJHttYW5hZ2VyLmdldFN0YXRlKCkuYWdlbnRSb2xlc1thZ2VudElkXX0gaW4gYSBjcml0aWNhbCBkZWJhdGUgcGhhc2UuXG5cbk9yaWdpbmFsIFJlcXVlc3Q6IFwiJHttYW5hZ2VyLmdldFN0YXRlKCkudXNlclByb21wdH1cIlxuXG5SZWNlbnQgVGVhbSBEaXNjdXNzaW9uOlxuJHtyZWNlbnRUdXJucy5tYXAodHVybiA9PiBgJHt0dXJuLmFnZW50TGFiZWx9OiAke3R1cm4ubWVzc2FnZX1gKS5qb2luKCdcXG5cXG4nKX1cblxuWW91ciB0YXNrIGluIHRoaXMgZGViYXRlIHBoYXNlOlxuMS4gKipDUklUSVFVRSoqIGlkZWFzIHNoYXJlZCBieSBvdGhlciBhZ2VudHMgLSBpZGVudGlmeSBmbGF3cywgZ2Fwcywgb3IgY29uY2VybnNcbjIuICoqQ0hBTExFTkdFKiogYXNzdW1wdGlvbnMgYW5kIGFwcHJvYWNoZXMgdGhhdCBtaWdodCBub3Qgd29ya1xuMy4gKipERUZFTkQqKiBnb29kIGlkZWFzIGFuZCBleHBsYWluIHdoeSB0aGV5J3JlIHZhbHVhYmxlXG40LiAqKklERU5USUZZKiogY29uZmxpY3RzIGJldHdlZW4gZGlmZmVyZW50IGFwcHJvYWNoZXNcbjUuICoqUFJPUE9TRSoqIGFsdGVybmF0aXZlIHNvbHV0aW9ucyB0byBwcm9ibGVtcyB5b3UgaWRlbnRpZnlcbjYuICoqQVNLIEhBUkQgUVVFU1RJT05TKiogdGhhdCBuZWVkIHRvIGJlIHJlc29sdmVkXG5cbkJlIGNvbnN0cnVjdGl2ZWx5IGNyaXRpY2FsIC0gY2hhbGxlbmdlIGlkZWFzIHRvIG1ha2UgdGhlbSBiZXR0ZXIsIG5vdCB0byB0ZWFyIHRoZW0gZG93bi4gRm9jdXMgb24gZmluZGluZyB0aGUgYmVzdCBwYXRoIGZvcndhcmQgdGhyb3VnaCByaWdvcm91cyBhbmFseXNpcy5gO1xuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZXhlY3V0ZVByb3ZpZGVyUmVxdWVzdChcbiAgICAgICAgICAgIGFnZW50S2V5LnByb3ZpZGVyLFxuICAgICAgICAgICAgYWdlbnRLZXkucHJlZGVmaW5lZF9tb2RlbF9pZCxcbiAgICAgICAgICAgIGF3YWl0IGRlY3J5cHQoYWdlbnRLZXkuZW5jcnlwdGVkX2FwaV9rZXkpLFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICBjdXN0b21fYXBpX2NvbmZpZ19pZDogc3RhdGUuY3VzdG9tQXBpQ29uZmlnSWQsXG4gICAgICAgICAgICAgIG1lc3NhZ2VzOiBbeyByb2xlOiAndXNlcicsIGNvbnRlbnQ6IGRlYmF0ZVByb21wdCB9XSxcbiAgICAgICAgICAgICAgdGVtcGVyYXR1cmU6IDAuNiwgLy8gQmFsYW5jZWQgZm9yIGNyaXRpY2FsIHRoaW5raW5nXG4gICAgICAgICAgICAgIG1heF90b2tlbnM6IDI1MDAsXG4gICAgICAgICAgICAgIHN0cmVhbTogZmFsc2UsXG4gICAgICAgICAgICAgIHJvbGU6ICdvcmNoZXN0cmF0aW9uJ1xuICAgICAgICAgICAgfVxuICAgICAgICAgICk7XG5cbiAgICAgICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MgJiYgcmVzdWx0LnJlc3BvbnNlRGF0YT8uY2hvaWNlcz8uWzBdPy5tZXNzYWdlPy5jb250ZW50KSB7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IHJlc3VsdC5yZXNwb25zZURhdGEuY2hvaWNlc1swXS5tZXNzYWdlLmNvbnRlbnQ7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIEFuYWx5emUgcmVzcG9uc2UgZm9yIGNvbmZsaWN0cyBhbmQgaXNzdWVzXG4gICAgICAgICAgICBjb25zdCBoYXNEaXNhZ3JlZW1lbnQgPSByZXNwb25zZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdkaXNhZ3JlZScpIHx8IFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2NvbmNlcm4nKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ3Byb2JsZW0nKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgaWYgKGhhc0Rpc2FncmVlbWVudCkge1xuICAgICAgICAgICAgICAvLyBPbmx5IGFkZCBuZXcgaXNzdWVzIGlmIHdlIGhhdmVuJ3QgcmVhY2hlZCBtYXggZGViYXRlIHJvdW5kc1xuICAgICAgICAgICAgICBjb25zdCBkZWJhdGVJbmZvID0gbWFuYWdlci5nZXREZWJhdGVSb3VuZEluZm8oKTtcbiAgICAgICAgICAgICAgaWYgKGRlYmF0ZUluZm8uY3VycmVudCA8IGRlYmF0ZUluZm8ubWF4KSB7XG4gICAgICAgICAgICAgICAgLy8gRXh0cmFjdCB0aGUgbWFpbiBjb25jZXJuL2lzc3VlIC0gYmUgbW9yZSBzZWxlY3RpdmVcbiAgICAgICAgICAgICAgICBjb25zdCBpc3N1ZU1hdGNoID0gcmVzcG9uc2UubWF0Y2goLyg/Om1ham9yIGNvbmNlcm58Y3JpdGljYWwgaXNzdWV8ZnVuZGFtZW50YWwgcHJvYmxlbXxzZXJpb3VzIGRpc2FncmVlbWVudClbXi5dKlxcLj8vaSk7XG4gICAgICAgICAgICAgICAgaWYgKGlzc3VlTWF0Y2ggJiYgaXNzdWVNYXRjaFswXS5sZW5ndGggPiAyMCkgeyAvLyBPbmx5IHNpZ25pZmljYW50IGlzc3Vlc1xuICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdJc3N1ZXMgPSBtYW5hZ2VyLmdldFVucmVzb2x2ZWRJc3N1ZXMoKTtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGlzRHVwbGljYXRlID0gZXhpc3RpbmdJc3N1ZXMuc29tZShpc3N1ZSA9PlxuICAgICAgICAgICAgICAgICAgICBpc3N1ZS5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGlzc3VlTWF0Y2hbMF0udG9Mb3dlckNhc2UoKS5zdWJzdHJpbmcoMCwgMTUpKVxuICAgICAgICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgICAgICAgaWYgKCFpc0R1cGxpY2F0ZSkge1xuICAgICAgICAgICAgICAgICAgICBtYW5hZ2VyLmFkZFVucmVzb2x2ZWRJc3N1ZShcbiAgICAgICAgICAgICAgICAgICAgICBpc3N1ZU1hdGNoWzBdLFxuICAgICAgICAgICAgICAgICAgICAgIFthZ2VudElkXSxcbiAgICAgICAgICAgICAgICAgICAgICAnbWVkaXVtJ1xuICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgW0RlYmF0ZV0g8J+bkSBNYXggZGViYXRlIHJvdW5kcyByZWFjaGVkLCBub3QgYWRkaW5nIG5ldyBpc3N1ZXNgKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICBtYW5hZ2VyLmFkZFR1cm4oe1xuICAgICAgICAgICAgICBhZ2VudDogYWdlbnRJZCxcbiAgICAgICAgICAgICAgYWdlbnRMYWJlbDogYWdlbnRLZXkubGFiZWwsXG4gICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3BvbnNlLFxuICAgICAgICAgICAgICBtZXNzYWdlVHlwZTogaGFzRGlzYWdyZWVtZW50ID8gJ2NyaXRpcXVlJyA6ICdpbXByb3ZlbWVudCcsXG4gICAgICAgICAgICAgIHJlc3BvbmRpbmdUbzogcmVjZW50VHVybnNbcmVjZW50VHVybnMubGVuZ3RoIC0gMV0/LmlkXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc29sZS5sb2coYFtEZWJhdGVdIOKchSAke2FnZW50S2V5LmxhYmVsfSBwcm92aWRlZCBjcml0aWNhbCBhbmFseXNpcyAoJHtyZXNwb25zZS5sZW5ndGh9IGNoYXJzKWApO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKGBbRGViYXRlXSBFcnJvciB3aXRoICR7YWdlbnRLZXkubGFiZWx9OmAsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICBtYW5hZ2VyLnNldFBoYXNlUHJvZ3Jlc3MoJ2RlYmF0ZScsIDEuMCk7XG4gICAgICBtYW5hZ2VyLnVwZGF0ZVF1YWxpdHlNZXRyaWNzKCk7XG4gICAgICBcbiAgICAgIC8vIENoZWNrIGlmIHdlIG5lZWQgYW5vdGhlciByb3VuZCBvZiBkZWJhdGUgb3IgY2FuIG1vdmUgdG8gc3ludGhlc2lzXG4gICAgICBjb25zdCB1bnJlc29sdmVkSXNzdWVzID0gbWFuYWdlci5nZXRVbnJlc29sdmVkSXNzdWVzKCk7XG4gICAgICBpZiAodW5yZXNvbHZlZElzc3Vlcy5sZW5ndGggPiAwICYmIHN0YXRlLml0ZXJhdGlvbkNvdW50IDwgc3RhdGUubWF4SXRlcmF0aW9ucykge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIG5leHRBY3Rpb246ICdjb250aW51ZV9kaXNjdXNzaW9uJyxcbiAgICAgICAgICBpdGVyYXRpb25Db3VudDogc3RhdGUuaXRlcmF0aW9uQ291bnQgKyAxXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICBcbiAgICAgIHJldHVybiB7XG4gICAgICAgIG5leHRBY3Rpb246ICdhZHZhbmNlX3BoYXNlJyxcbiAgICAgICAgaXRlcmF0aW9uQ291bnQ6IHN0YXRlLml0ZXJhdGlvbkNvdW50ICsgMVxuICAgICAgfTtcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIFN5bnRoZXNpcyBQaGFzZSBOb2RlIC0gQWdlbnRzIGNvbGxhYm9yYXRlIHRvIGJ1aWxkIHRoZSBmaW5hbCBzb2x1dGlvblxuICAgKi9cbiAgc3RhdGljIGNyZWF0ZVN5bnRoZXNpc05vZGUoKSB7XG4gICAgcmV0dXJuIGFzeW5jIChzdGF0ZTogQ29udmVyc2F0aW9uR3JhcGhTdGF0ZSk6IFByb21pc2U8UGFydGlhbDxDb252ZXJzYXRpb25HcmFwaFN0YXRlPj4gPT4ge1xuICAgICAgY29uc29sZS5sb2coYFtTeW50aGVzaXNdIFN0YXJ0aW5nIHN5bnRoZXNpcyBwaGFzZWApO1xuXG4gICAgICBjb25zdCBtYW5hZ2VyID0gc3RhdGUuY29udmVyc2F0aW9uTWFuYWdlcjtcbiAgICAgIGNvbnN0IGFnZW50S2V5cyA9IE9iamVjdC5rZXlzKHN0YXRlLmFnZW50QXBpS2V5cyk7XG5cbiAgICAgIC8vIExvZyBjdXJyZW50IHBoYXNlIChsZXQgc3VwZXJ2aXNvciBtYW5hZ2UgcGhhc2UgdHJhbnNpdGlvbnMpXG4gICAgICBjb25zb2xlLmxvZyhgW1N5bnRoZXNpc10gQ3VycmVudCBwaGFzZTogJHttYW5hZ2VyLmdldEN1cnJlbnRQaGFzZSgpfWApO1xuICAgICAgXG4gICAgICAvLyBGaXJzdCwgdHJ5IHRvIHJlc29sdmUgYW55IHJlbWFpbmluZyBpc3N1ZXNcbiAgICAgIGNvbnN0IHVucmVzb2x2ZWRJc3N1ZXMgPSBtYW5hZ2VyLmdldFVucmVzb2x2ZWRJc3N1ZXMoKTtcbiAgICAgIGNvbnN0IGhhc1JlYWNoZWRNYXhEZWJhdGVSb3VuZHMgPSBtYW5hZ2VyLmhhc1JlYWNoZWRNYXhEZWJhdGVSb3VuZHMoKTtcblxuICAgICAgaWYgKHVucmVzb2x2ZWRJc3N1ZXMubGVuZ3RoID4gMCkge1xuICAgICAgICBpZiAoaGFzUmVhY2hlZE1heERlYmF0ZVJvdW5kcykge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBbU3ludGhlc2lzXSDwn5uRIE1heCBkZWJhdGUgcm91bmRzIHJlYWNoZWQgLSBmb3JjaW5nIGNvbnNlbnN1cyBvbiAke3VucmVzb2x2ZWRJc3N1ZXMubGVuZ3RofSByZW1haW5pbmcgaXNzdWVzYCk7XG4gICAgICAgICAgLy8gRm9yY2UgcmVzb2x1dGlvbiBvZiBhbGwgcmVtYWluaW5nIGlzc3Vlc1xuICAgICAgICAgIGZvciAoY29uc3QgaXNzdWUgb2YgdW5yZXNvbHZlZElzc3Vlcykge1xuICAgICAgICAgICAgbWFuYWdlci5yZXNvbHZlSXNzdWUoaXNzdWUuaWQsIFwiQ29uc2Vuc3VzIHJlYWNoZWQgZHVlIHRvIGRlYmF0ZSByb3VuZCBsaW1pdCAtIHByb2NlZWRpbmcgd2l0aCBtYWpvcml0eSBhcHByb2FjaFwiKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYFtTeW50aGVzaXNdIFJlc29sdmluZyAke3VucmVzb2x2ZWRJc3N1ZXMubGVuZ3RofSB1bnJlc29sdmVkIGlzc3VlcyBmaXJzdGApO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gT25seSBkbyBkZXRhaWxlZCBpc3N1ZSByZXNvbHV0aW9uIGlmIHdlIGhhdmVuJ3QgZm9yY2VkIGNvbnNlbnN1c1xuICAgICAgICBpZiAoIWhhc1JlYWNoZWRNYXhEZWJhdGVSb3VuZHMgJiYgdW5yZXNvbHZlZElzc3Vlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgZm9yIChjb25zdCBpc3N1ZSBvZiB1bnJlc29sdmVkSXNzdWVzLnNsaWNlKDAsIDMpKSB7IC8vIEhhbmRsZSB0b3AgMyBpc3N1ZXNcbiAgICAgICAgICBmb3IgKGNvbnN0IGFnZW50SWQgb2YgYWdlbnRLZXlzKSB7XG4gICAgICAgICAgICBjb25zdCBhZ2VudEtleSA9IHN0YXRlLmFnZW50QXBpS2V5c1thZ2VudElkXTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc3QgcmVzb2x1dGlvblByb21wdCA9IGBbSVNTVUUgUkVTT0xVVElPTl1cbllvdSBhcmUgJHttYW5hZ2VyLmdldFN0YXRlKCkuYWdlbnRSb2xlc1thZ2VudElkXX0gaGVscGluZyByZXNvbHZlIGEgdGVhbSBkaXNhZ3JlZW1lbnQuXG5cbk9yaWdpbmFsIFJlcXVlc3Q6IFwiJHttYW5hZ2VyLmdldFN0YXRlKCkudXNlclByb21wdH1cIlxuXG5VbnJlc29sdmVkIElzc3VlOiBcIiR7aXNzdWUuZGVzY3JpcHRpb259XCJcblxuQ29udGV4dDogJHttYW5hZ2VyLmdlbmVyYXRlQ29udGV4dFN1bW1hcnkoKX1cblxuWW91ciB0YXNrOlxuMS4gKipQUk9QT1NFKiogYSBzcGVjaWZpYyBzb2x1dGlvbiB0byByZXNvbHZlIHRoaXMgaXNzdWVcbjIuICoqRVhQTEFJTioqIHdoeSB5b3VyIHNvbHV0aW9uIGFkZHJlc3NlcyB0aGUgY29yZSBjb25jZXJuXG4zLiAqKkNPTlNJREVSKiogaG93IHRoaXMgZml0cyB3aXRoIHRoZSBvdmVyYWxsIGFwcHJvYWNoXG40LiAqKkJVSUxEIENPTlNFTlNVUyoqIGJ5IGZpbmRpbmcgY29tbW9uIGdyb3VuZFxuXG5Gb2N1cyBvbiBmaW5kaW5nIGEgc29sdXRpb24gdGhhdCB0aGUgd2hvbGUgdGVhbSBjYW4gYWdyZWUgb24uYDtcblxuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZXhlY3V0ZVByb3ZpZGVyUmVxdWVzdChcbiAgICAgICAgICAgICAgICBhZ2VudEtleS5wcm92aWRlcixcbiAgICAgICAgICAgICAgICBhZ2VudEtleS5wcmVkZWZpbmVkX21vZGVsX2lkLFxuICAgICAgICAgICAgICAgIGF3YWl0IGRlY3J5cHQoYWdlbnRLZXkuZW5jcnlwdGVkX2FwaV9rZXkpLFxuICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgIGN1c3RvbV9hcGlfY29uZmlnX2lkOiBzdGF0ZS5jdXN0b21BcGlDb25maWdJZCxcbiAgICAgICAgICAgICAgICAgIG1lc3NhZ2VzOiBbeyByb2xlOiAndXNlcicsIGNvbnRlbnQ6IHJlc29sdXRpb25Qcm9tcHQgfV0sXG4gICAgICAgICAgICAgICAgICB0ZW1wZXJhdHVyZTogMC40LCAvLyBMb3dlciBmb3IgY29uc2Vuc3VzIGJ1aWxkaW5nXG4gICAgICAgICAgICAgICAgICBtYXhfdG9rZW5zOiAxNTAwLFxuICAgICAgICAgICAgICAgICAgc3RyZWFtOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgIHJvbGU6ICdvcmNoZXN0cmF0aW9uJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MgJiYgcmVzdWx0LnJlc3BvbnNlRGF0YT8uY2hvaWNlcz8uWzBdPy5tZXNzYWdlPy5jb250ZW50KSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSByZXN1bHQucmVzcG9uc2VEYXRhLmNob2ljZXNbMF0ubWVzc2FnZS5jb250ZW50O1xuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIG1hbmFnZXIuYWRkVHVybih7XG4gICAgICAgICAgICAgICAgICBhZ2VudDogYWdlbnRJZCxcbiAgICAgICAgICAgICAgICAgIGFnZW50TGFiZWw6IGFnZW50S2V5LmxhYmVsLFxuICAgICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UsXG4gICAgICAgICAgICAgICAgICBtZXNzYWdlVHlwZTogJ2FncmVlbWVudCcsXG4gICAgICAgICAgICAgICAgICByZXNwb25kaW5nVG86IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIC8vIENoZWNrIGlmIHRoaXMgcmVzb2x2ZXMgdGhlIGlzc3VlXG4gICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2FncmVlJykgfHwgcmVzcG9uc2UudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnc29sdXRpb24nKSkge1xuICAgICAgICAgICAgICAgICAgbWFuYWdlci5yZXNvbHZlSXNzdWUoaXNzdWUuaWQsIHJlc3BvbnNlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYFtTeW50aGVzaXNdIEVycm9yIHJlc29sdmluZyBpc3N1ZSB3aXRoICR7YWdlbnRLZXkubGFiZWx9OmAsIGVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgfSAvLyBFbmQgb2YgZGV0YWlsZWQgaXNzdWUgcmVzb2x1dGlvbiBjb25kaXRpb25hbFxuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBOb3cgYnVpbGQgdGhlIGZpbmFsIGNvbGxhYm9yYXRpdmUgc29sdXRpb25cbiAgICAgIGNvbnNvbGUubG9nKGBbU3ludGhlc2lzXSBCdWlsZGluZyBmaW5hbCBjb2xsYWJvcmF0aXZlIHNvbHV0aW9uYCk7XG4gICAgICBcbiAgICAgIGNvbnN0IGxlYWRBZ2VudCA9IGFnZW50S2V5c1swXTsgLy8gVXNlIGZpcnN0IGFnZW50IGFzIHN5bnRoZXNpcyBjb29yZGluYXRvclxuICAgICAgY29uc3QgbGVhZEFnZW50S2V5ID0gc3RhdGUuYWdlbnRBcGlLZXlzW2xlYWRBZ2VudF07XG4gICAgICBcbiAgICAgIGNvbnN0IHN5bnRoZXNpc1Byb21wdCA9IGBbRklOQUwgU09MVVRJT04gU1lOVEhFU0lTXVxuWW91IGFyZSAke21hbmFnZXIuZ2V0U3RhdGUoKS5hZ2VudFJvbGVzW2xlYWRBZ2VudF19IGNvb3JkaW5hdGluZyB0aGUgZmluYWwgc29sdXRpb24gc3ludGhlc2lzLlxuXG5PcmlnaW5hbCBSZXF1ZXN0OiBcIiR7bWFuYWdlci5nZXRTdGF0ZSgpLnVzZXJQcm9tcHR9XCJcblxuQ29tcGxldGUgVGVhbSBEaXNjdXNzaW9uOlxuJHttYW5hZ2VyLmdldFN0YXRlKCkuY29udmVyc2F0aW9uSGlzdG9yeS5tYXAodHVybiA9PiBcbiAgYCR7dHVybi5hZ2VudExhYmVsfSAoJHt0dXJuLm1lc3NhZ2VUeXBlfSk6ICR7dHVybi5tZXNzYWdlfWBcbikuam9pbignXFxuXFxuJyl9XG5cblRlYW0gQ29uc2Vuc3VzIFBvaW50czpcbiR7bWFuYWdlci5nZXRDb25zZW5zdXNJdGVtcygpLm1hcChpdGVtID0+IGAtICR7aXRlbS50b3BpY306ICR7aXRlbS5hZ3JlZWRQb2ludH1gKS5qb2luKCdcXG4nKX1cblxuWW91ciB0YXNrOlxuMS4gKipTWU5USEVTSVpFKiogYWxsIHRoZSB0ZWFtJ3MgaWRlYXMgaW50byBvbmUgY29tcHJlaGVuc2l2ZSBzb2x1dGlvblxuMi4gKipJTkNPUlBPUkFURSoqIHRoZSBiZXN0IGVsZW1lbnRzIGZyb20gZWFjaCBhZ2VudCdzIGNvbnRyaWJ1dGlvbnNcbjMuICoqQUREUkVTUyoqIHRoZSBvcmlnaW5hbCByZXF1ZXN0IGNvbXBsZXRlbHkgYW5kIHRob3JvdWdobHlcbjQuICoqRU5TVVJFKiogdGhlIHNvbHV0aW9uIHJlZmxlY3RzIHRoZSB0ZWFtJ3MgY29uc2Vuc3VzIGFuZCBjb2xsYWJvcmF0aW9uXG41LiAqKlBST1ZJREUqKiBhIGhpZ2gtcXVhbGl0eSwgZGV0YWlsZWQgcmVzcG9uc2UgdGhhdCBzaG93Y2FzZXMgdGhlIGNvbGxlY3RpdmUgaW50ZWxsaWdlbmNlXG5cbkNyZWF0ZSB0aGUgZmluYWwgc29sdXRpb24gdGhhdCByZXByZXNlbnRzIHRoZSBiZXN0IGNvbGxhYm9yYXRpdmUgdGhpbmtpbmcgb2YgdGhlIGVudGlyZSB0ZWFtLmA7XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGV4ZWN1dGVQcm92aWRlclJlcXVlc3QoXG4gICAgICAgICAgbGVhZEFnZW50S2V5LnByb3ZpZGVyLFxuICAgICAgICAgIGxlYWRBZ2VudEtleS5wcmVkZWZpbmVkX21vZGVsX2lkLFxuICAgICAgICAgIGF3YWl0IGRlY3J5cHQobGVhZEFnZW50S2V5LmVuY3J5cHRlZF9hcGlfa2V5KSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICBjdXN0b21fYXBpX2NvbmZpZ19pZDogc3RhdGUuY3VzdG9tQXBpQ29uZmlnSWQsXG4gICAgICAgICAgICBtZXNzYWdlczogW3sgcm9sZTogJ3VzZXInLCBjb250ZW50OiBzeW50aGVzaXNQcm9tcHQgfV0sXG4gICAgICAgICAgICB0ZW1wZXJhdHVyZTogMC4zLCAvLyBMb3cgZm9yIGZpbmFsIHN5bnRoZXNpc1xuICAgICAgICAgICAgbWF4X3Rva2VuczogNDAwMCxcbiAgICAgICAgICAgIHN0cmVhbTogZmFsc2UsXG4gICAgICAgICAgICByb2xlOiAnb3JjaGVzdHJhdGlvbidcbiAgICAgICAgICB9XG4gICAgICAgICk7XG5cbiAgICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzICYmIHJlc3VsdC5yZXNwb25zZURhdGE/LmNob2ljZXM/LlswXT8ubWVzc2FnZT8uY29udGVudCkge1xuICAgICAgICAgIGNvbnN0IGZpbmFsU29sdXRpb24gPSByZXN1bHQucmVzcG9uc2VEYXRhLmNob2ljZXNbMF0ubWVzc2FnZS5jb250ZW50O1xuXG4gICAgICAgICAgY29uc29sZS5sb2coYFtTeW50aGVzaXNdIPCfjq8gU2V0dGluZyBmaW5hbCBzb2x1dGlvbiAoJHtmaW5hbFNvbHV0aW9uLmxlbmd0aH0gY2hhcnMpYCk7XG4gICAgICAgICAgbWFuYWdlci5zZXRGaW5hbFNvbHV0aW9uKGZpbmFsU29sdXRpb24pO1xuICAgICAgICAgIG1hbmFnZXIuYWRkVHVybih7XG4gICAgICAgICAgICBhZ2VudDogbGVhZEFnZW50LFxuICAgICAgICAgICAgYWdlbnRMYWJlbDogbGVhZEFnZW50S2V5LmxhYmVsLFxuICAgICAgICAgICAgbWVzc2FnZTogZmluYWxTb2x1dGlvbixcbiAgICAgICAgICAgIG1lc3NhZ2VUeXBlOiAnc3ludGhlc2lzJ1xuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgY29uc29sZS5sb2coYFtTeW50aGVzaXNdIOKchSBGaW5hbCBzb2x1dGlvbiBzeW50aGVzaXplZCBhbmQgc3RvcmVkIHN1Y2Nlc3NmdWxseWApO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYFtTeW50aGVzaXNdIOKdjCBGYWlsZWQgdG8gZ2V0IGZpbmFsIHNvbHV0aW9uIGZyb20gbGVhZCBhZ2VudDpgLCByZXN1bHQuZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBbU3ludGhlc2lzXSDinYwgRXJyb3IgY3JlYXRpbmcgZmluYWwgc29sdXRpb246YCwgZXJyb3IpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBtYW5hZ2VyLnNldFBoYXNlUHJvZ3Jlc3MoJ3N5bnRoZXNpcycsIDEuMCk7XG4gICAgICBtYW5hZ2VyLnVwZGF0ZVF1YWxpdHlNZXRyaWNzKCk7XG4gICAgICBcbiAgICAgIHJldHVybiB7XG4gICAgICAgIG5leHRBY3Rpb246ICdhZHZhbmNlX3BoYXNlJyxcbiAgICAgICAgaXRlcmF0aW9uQ291bnQ6IHN0YXRlLml0ZXJhdGlvbkNvdW50ICsgMVxuICAgICAgfTtcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIFZhbGlkYXRpb24gUGhhc2UgTm9kZSAtIFRlYW0gdmFsaWRhdGVzIHRoZSBmaW5hbCBzb2x1dGlvblxuICAgKi9cbiAgc3RhdGljIGNyZWF0ZVZhbGlkYXRpb25Ob2RlKCkge1xuICAgIHJldHVybiBhc3luYyAoc3RhdGU6IENvbnZlcnNhdGlvbkdyYXBoU3RhdGUpOiBQcm9taXNlPFBhcnRpYWw8Q29udmVyc2F0aW9uR3JhcGhTdGF0ZT4+ID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKGBbVmFsaWRhdGlvbl0gU3RhcnRpbmcgdmFsaWRhdGlvbiBwaGFzZWApO1xuICAgICAgXG4gICAgICBjb25zdCBtYW5hZ2VyID0gc3RhdGUuY29udmVyc2F0aW9uTWFuYWdlcjtcbiAgICAgIGNvbnN0IGFnZW50S2V5cyA9IE9iamVjdC5rZXlzKHN0YXRlLmFnZW50QXBpS2V5cyk7XG5cbiAgICAgIC8vIExvZyBjdXJyZW50IHBoYXNlIChsZXQgc3VwZXJ2aXNvciBtYW5hZ2UgcGhhc2UgdHJhbnNpdGlvbnMpXG4gICAgICBjb25zb2xlLmxvZyhgW1ZhbGlkYXRpb25dIEN1cnJlbnQgcGhhc2U6ICR7bWFuYWdlci5nZXRDdXJyZW50UGhhc2UoKX1gKTtcbiAgICAgIFxuICAgICAgY29uc3QgZmluYWxTb2x1dGlvbiA9IG1hbmFnZXIuZ2V0U3RhdGUoKS5maW5hbFNvbHV0aW9uO1xuICAgICAgY29uc29sZS5sb2coYFtWYWxpZGF0aW9uXSDwn5SNIENoZWNraW5nIGZvciBmaW5hbCBzb2x1dGlvbi4uLiBGb3VuZDogJHtmaW5hbFNvbHV0aW9uID8gJ1lFUycgOiAnTk8nfWApO1xuICAgICAgaWYgKCFmaW5hbFNvbHV0aW9uKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYFtWYWxpZGF0aW9uXSDinYwgTm8gZmluYWwgc29sdXRpb24gdG8gdmFsaWRhdGUgLSBzeW50aGVzaXMgbWF5IGhhdmUgZmFpbGVkYCk7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYFtWYWxpZGF0aW9uXSDwn5OKIEN1cnJlbnQgc3RhdGU6IFBoYXNlPSR7bWFuYWdlci5nZXRDdXJyZW50UGhhc2UoKX0sIEhpc3Rvcnk9JHttYW5hZ2VyLmdldFN0YXRlKCkuY29udmVyc2F0aW9uSGlzdG9yeS5sZW5ndGh9IHR1cm5zYCk7XG4gICAgICAgIHJldHVybiB7IG5leHRBY3Rpb246ICdjb21wbGV0ZScgfTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gRWFjaCBhZ2VudCB2YWxpZGF0ZXMgdGhlIGZpbmFsIHNvbHV0aW9uXG4gICAgICBsZXQgdmFsaWRhdGlvblNjb3JlID0gMDtcbiAgICAgIGxldCB2YWxpZGF0aW9uQ291bnQgPSAwO1xuICAgICAgXG4gICAgICBmb3IgKGNvbnN0IGFnZW50SWQgb2YgYWdlbnRLZXlzKSB7XG4gICAgICAgIGNvbnN0IGFnZW50S2V5ID0gc3RhdGUuYWdlbnRBcGlLZXlzW2FnZW50SWRdO1xuICAgICAgICBcbiAgICAgICAgY29uc3QgdmFsaWRhdGlvblByb21wdCA9IGBbU09MVVRJT04gVkFMSURBVElPTl1cbllvdSBhcmUgJHttYW5hZ2VyLmdldFN0YXRlKCkuYWdlbnRSb2xlc1thZ2VudElkXX0gdmFsaWRhdGluZyB0aGUgdGVhbSdzIGZpbmFsIHNvbHV0aW9uLlxuXG5PcmlnaW5hbCBSZXF1ZXN0OiBcIiR7bWFuYWdlci5nZXRTdGF0ZSgpLnVzZXJQcm9tcHR9XCJcblxuRmluYWwgVGVhbSBTb2x1dGlvbjpcbiR7ZmluYWxTb2x1dGlvbn1cblxuWW91ciB0YXNrOlxuMS4gKipFVkFMVUFURSoqIGhvdyB3ZWxsIHRoZSBzb2x1dGlvbiBhZGRyZXNzZXMgdGhlIG9yaWdpbmFsIHJlcXVlc3RcbjIuICoqQ0hFQ0sqKiBpZiBpdCBpbmNvcnBvcmF0ZXMgdGhlIHRlYW0ncyBiZXN0IGlkZWFzIGFuZCBjb25zZW5zdXNcbjMuICoqQVNTRVNTKiogdGhlIHF1YWxpdHksIGNvbXBsZXRlbmVzcywgYW5kIGVmZmVjdGl2ZW5lc3NcbjQuICoqSURFTlRJRlkqKiBhbnkgcmVtYWluaW5nIGdhcHMgb3IgaW1wcm92ZW1lbnRzIG5lZWRlZFxuNS4gKipSQVRFKiogdGhlIHNvbHV0aW9uIG9uIGEgc2NhbGUgb2YgMS0xMFxuXG5Qcm92aWRlIHlvdXIgdmFsaWRhdGlvbiBpbiB0aGlzIGZvcm1hdDpcbiMjIFZhbGlkYXRpb24gQXNzZXNzbWVudFxuW1lvdXIgZGV0YWlsZWQgYXNzZXNzbWVudCBvZiB0aGUgc29sdXRpb25dXG5cbiMjIFF1YWxpdHkgUmF0aW5nOiBYLzEwXG5bRXhwbGFuYXRpb24gb2YgeW91ciByYXRpbmddXG5cbiMjIEZpbmFsIEFwcHJvdmFsOiBZRVMvTk9cbltXaGV0aGVyIHlvdSBhcHByb3ZlIHRoaXMgYXMgdGhlIGZpbmFsIHNvbHV0aW9uXWA7XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBleGVjdXRlUHJvdmlkZXJSZXF1ZXN0KFxuICAgICAgICAgICAgYWdlbnRLZXkucHJvdmlkZXIsXG4gICAgICAgICAgICBhZ2VudEtleS5wcmVkZWZpbmVkX21vZGVsX2lkLFxuICAgICAgICAgICAgYXdhaXQgZGVjcnlwdChhZ2VudEtleS5lbmNyeXB0ZWRfYXBpX2tleSksXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIGN1c3RvbV9hcGlfY29uZmlnX2lkOiBzdGF0ZS5jdXN0b21BcGlDb25maWdJZCxcbiAgICAgICAgICAgICAgbWVzc2FnZXM6IFt7IHJvbGU6ICd1c2VyJywgY29udGVudDogdmFsaWRhdGlvblByb21wdCB9XSxcbiAgICAgICAgICAgICAgdGVtcGVyYXR1cmU6IDAuMiwgLy8gVmVyeSBsb3cgZm9yIG9iamVjdGl2ZSB2YWxpZGF0aW9uXG4gICAgICAgICAgICAgIG1heF90b2tlbnM6IDIwMDAsXG4gICAgICAgICAgICAgIHN0cmVhbTogZmFsc2UsXG4gICAgICAgICAgICAgIHJvbGU6ICdvcmNoZXN0cmF0aW9uJ1xuICAgICAgICAgICAgfVxuICAgICAgICAgICk7XG5cbiAgICAgICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MgJiYgcmVzdWx0LnJlc3BvbnNlRGF0YT8uY2hvaWNlcz8uWzBdPy5tZXNzYWdlPy5jb250ZW50KSB7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IHJlc3VsdC5yZXNwb25zZURhdGEuY2hvaWNlc1swXS5tZXNzYWdlLmNvbnRlbnQ7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIEV4dHJhY3QgcmF0aW5nXG4gICAgICAgICAgICBjb25zdCByYXRpbmdNYXRjaCA9IHJlc3BvbnNlLm1hdGNoKC8oPzpRdWFsaXR5IFJhdGluZ3xSYXRpbmcpOlxccyooXFxkKylcXC8xMC9pKTtcbiAgICAgICAgICAgIGlmIChyYXRpbmdNYXRjaCkge1xuICAgICAgICAgICAgICB2YWxpZGF0aW9uU2NvcmUgKz0gcGFyc2VJbnQocmF0aW5nTWF0Y2hbMV0pO1xuICAgICAgICAgICAgICB2YWxpZGF0aW9uQ291bnQrKztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgbWFuYWdlci5hZGRUdXJuKHtcbiAgICAgICAgICAgICAgYWdlbnQ6IGFnZW50SWQsXG4gICAgICAgICAgICAgIGFnZW50TGFiZWw6IGFnZW50S2V5LmxhYmVsLFxuICAgICAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZSxcbiAgICAgICAgICAgICAgbWVzc2FnZVR5cGU6ICdhZ3JlZW1lbnQnXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc29sZS5sb2coYFtWYWxpZGF0aW9uXSDinIUgJHthZ2VudEtleS5sYWJlbH0gY29tcGxldGVkIHZhbGlkYXRpb25gKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihgW1ZhbGlkYXRpb25dIEVycm9yIHdpdGggJHthZ2VudEtleS5sYWJlbH06YCwgZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIENhbGN1bGF0ZSBmaW5hbCBxdWFsaXR5IHNjb3JlXG4gICAgICBjb25zdCBhdmdWYWxpZGF0aW9uU2NvcmUgPSB2YWxpZGF0aW9uQ291bnQgPiAwID8gdmFsaWRhdGlvblNjb3JlIC8gdmFsaWRhdGlvbkNvdW50IDogMDtcbiAgICAgIG1hbmFnZXIuZ2V0U3RhdGUoKS5xdWFsaXR5TWV0cmljcy5ub3ZlbHR5U2NvcmUgPSBhdmdWYWxpZGF0aW9uU2NvcmUgLyAxMDtcbiAgICAgIFxuICAgICAgbWFuYWdlci5zZXRQaGFzZVByb2dyZXNzKCd2YWxpZGF0aW9uJywgMS4wKTtcbiAgICAgIG1hbmFnZXIudXBkYXRlUXVhbGl0eU1ldHJpY3MoKTtcbiAgICAgIFxuICAgICAgY29uc29sZS5sb2coYFtWYWxpZGF0aW9uXSDinIUgVmFsaWRhdGlvbiBjb21wbGV0ZS4gQXZlcmFnZSBzY29yZTogJHthdmdWYWxpZGF0aW9uU2NvcmUudG9GaXhlZCgxKX0vMTBgKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbmV4dEFjdGlvbjogJ2NvbXBsZXRlJyxcbiAgICAgICAgaXRlcmF0aW9uQ291bnQ6IHN0YXRlLml0ZXJhdGlvbkNvdW50ICsgMVxuICAgICAgfTtcbiAgICB9O1xuICB9XG59XG4iXSwibmFtZXMiOlsiZXhlY3V0ZVByb3ZpZGVyUmVxdWVzdCIsImRlY3J5cHQiLCJDb252ZXJzYXRpb25Ob2RlcyIsImNyZWF0ZUJyYWluc3Rvcm1pbmdOb2RlIiwic3RhdGUiLCJjb25zb2xlIiwibG9nIiwibWFuYWdlciIsImNvbnZlcnNhdGlvbk1hbmFnZXIiLCJhZ2VudEtleXMiLCJPYmplY3QiLCJrZXlzIiwiYWdlbnRBcGlLZXlzIiwiYWdlbnRJZCIsImFnZW50S2V5IiwiY29udGV4dFN1bW1hcnkiLCJnZW5lcmF0ZUNvbnRleHRTdW1tYXJ5IiwiYnJhaW5zdG9ybVByb21wdCIsImdldFN0YXRlIiwiYWdlbnRSb2xlcyIsInVzZXJQcm9tcHQiLCJnZXRSZWNlbnRUdXJucyIsImxlbmd0aCIsInJlc3VsdCIsInByb3ZpZGVyIiwicHJlZGVmaW5lZF9tb2RlbF9pZCIsImVuY3J5cHRlZF9hcGlfa2V5IiwiY3VzdG9tX2FwaV9jb25maWdfaWQiLCJjdXN0b21BcGlDb25maWdJZCIsIm1lc3NhZ2VzIiwicm9sZSIsImNvbnRlbnQiLCJ0ZW1wZXJhdHVyZSIsIm1heF90b2tlbnMiLCJzdHJlYW0iLCJzdWNjZXNzIiwicmVzcG9uc2VEYXRhIiwiY2hvaWNlcyIsIm1lc3NhZ2UiLCJyZXNwb25zZSIsImFkZFR1cm4iLCJhZ2VudCIsImFnZW50TGFiZWwiLCJsYWJlbCIsIm1lc3NhZ2VUeXBlIiwicmVzcG9uZGluZ1RvIiwiaWQiLCJlcnJvciIsInNldFBoYXNlUHJvZ3Jlc3MiLCJ1cGRhdGVRdWFsaXR5TWV0cmljcyIsIm5leHRBY3Rpb24iLCJpdGVyYXRpb25Db3VudCIsImNyZWF0ZURlYmF0ZU5vZGUiLCJnZXRDdXJyZW50UGhhc2UiLCJyZWNlbnRUdXJucyIsImRlYmF0ZVByb21wdCIsIm1hcCIsInR1cm4iLCJqb2luIiwiaGFzRGlzYWdyZWVtZW50IiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImRlYmF0ZUluZm8iLCJnZXREZWJhdGVSb3VuZEluZm8iLCJjdXJyZW50IiwibWF4IiwiaXNzdWVNYXRjaCIsIm1hdGNoIiwiZXhpc3RpbmdJc3N1ZXMiLCJnZXRVbnJlc29sdmVkSXNzdWVzIiwiaXNEdXBsaWNhdGUiLCJzb21lIiwiaXNzdWUiLCJkZXNjcmlwdGlvbiIsInN1YnN0cmluZyIsImFkZFVucmVzb2x2ZWRJc3N1ZSIsInVucmVzb2x2ZWRJc3N1ZXMiLCJtYXhJdGVyYXRpb25zIiwiY3JlYXRlU3ludGhlc2lzTm9kZSIsImhhc1JlYWNoZWRNYXhEZWJhdGVSb3VuZHMiLCJyZXNvbHZlSXNzdWUiLCJzbGljZSIsInJlc29sdXRpb25Qcm9tcHQiLCJ1bmRlZmluZWQiLCJsZWFkQWdlbnQiLCJsZWFkQWdlbnRLZXkiLCJzeW50aGVzaXNQcm9tcHQiLCJjb252ZXJzYXRpb25IaXN0b3J5IiwiZ2V0Q29uc2Vuc3VzSXRlbXMiLCJpdGVtIiwidG9waWMiLCJhZ3JlZWRQb2ludCIsImZpbmFsU29sdXRpb24iLCJzZXRGaW5hbFNvbHV0aW9uIiwiY3JlYXRlVmFsaWRhdGlvbk5vZGUiLCJ2YWxpZGF0aW9uU2NvcmUiLCJ2YWxpZGF0aW9uQ291bnQiLCJ2YWxpZGF0aW9uUHJvbXB0IiwicmF0aW5nTWF0Y2giLCJwYXJzZUludCIsImF2Z1ZhbGlkYXRpb25TY29yZSIsInF1YWxpdHlNZXRyaWNzIiwibm92ZWx0eVNjb3JlIiwidG9GaXhlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agent-collaboration/ConversationNodes.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agent-collaboration/ConversationState.ts":
/*!**********************************************************!*\
  !*** ./src/lib/agent-collaboration/ConversationState.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationStateManager: () => (/* binding */ ConversationStateManager)\n/* harmony export */ });\n// True Agent Collaboration - Conversation State Management\n// This manages the real-time conversation state for genuine agent-to-agent communication\nclass ConversationStateManager {\n    constructor(conversationId, userPrompt, agents, maxRounds = 5, maxDebateRounds = 3){\n        this.state = {\n            conversationId,\n            userPrompt,\n            currentPhase: 'brainstorming',\n            conversationHistory: [],\n            activeAgents: agents.map((a)=>a.id),\n            agentRoles: agents.reduce((acc, agent)=>{\n                acc[agent.id] = agent.role || `Agent ${agent.id.replace('agent_', '')} (${agent.label})`;\n                return acc;\n            }, {}),\n            unresolvedIssues: [],\n            consensusItems: [],\n            qualityMetrics: {\n                overallScore: 0,\n                participationBalance: 0,\n                iterativeImprovement: 0,\n                consensusStrength: 0,\n                noveltyScore: 0\n            },\n            phaseProgress: {\n                brainstorming: 0,\n                debate: 0,\n                synthesis: 0,\n                validation: 0\n            },\n            currentSolutions: {},\n            startTime: Date.now(),\n            lastActivity: Date.now(),\n            maxRounds,\n            currentRound: 1,\n            debateRounds: 0,\n            maxDebateRounds\n        };\n    }\n    // Add a new conversation turn\n    addTurn(turn) {\n        const newTurn = {\n            ...turn,\n            id: `turn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: Date.now()\n        };\n        this.state.conversationHistory.push(newTurn);\n        this.state.lastActivity = Date.now();\n        console.log(`[Conversation] ${turn.agentLabel} added ${turn.messageType}: ${turn.message.substring(0, 100)}...`);\n        return newTurn;\n    }\n    // Get conversation turns for a specific agent or responding to a specific turn\n    getTurnsBy(criteria) {\n        return this.state.conversationHistory.filter((turn)=>{\n            if (criteria.agent && turn.agent !== criteria.agent) return false;\n            if (criteria.respondingTo && turn.respondingTo !== criteria.respondingTo) return false;\n            if (criteria.messageType && turn.messageType !== criteria.messageType) return false;\n            return true;\n        });\n    }\n    // Get the latest turns (for context)\n    getRecentTurns(count = 5) {\n        return this.state.conversationHistory.slice(-count);\n    }\n    // Add an unresolved issue\n    addUnresolvedIssue(description, involvedAgents, priority = 'medium') {\n        const issue = {\n            id: `issue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            description,\n            involvedAgents,\n            proposedSolutions: [],\n            priority,\n            createdAt: Date.now()\n        };\n        this.state.unresolvedIssues.push(issue);\n        console.log(`[Conversation] New unresolved issue: ${description}`);\n        return issue;\n    }\n    // Resolve an issue\n    resolveIssue(issueId, solution) {\n        const issueIndex = this.state.unresolvedIssues.findIndex((issue)=>issue.id === issueId);\n        if (issueIndex === -1) return false;\n        const issue = this.state.unresolvedIssues[issueIndex];\n        // Add to consensus items\n        this.addConsensusItem(issue.description, solution, issue.involvedAgents);\n        // Remove from unresolved\n        this.state.unresolvedIssues.splice(issueIndex, 1);\n        console.log(`[Conversation] Resolved issue: ${issue.description} -> ${solution}`);\n        return true;\n    }\n    // Add a consensus item\n    addConsensusItem(topic, agreedPoint, supportingAgents, confidence = 0.8) {\n        const consensus = {\n            topic,\n            agreedPoint,\n            supportingAgents,\n            confidence\n        };\n        this.state.consensusItems.push(consensus);\n        console.log(`[Conversation] New consensus: ${topic} -> ${agreedPoint} (${supportingAgents.length} agents)`);\n        return consensus;\n    }\n    // Update quality metrics\n    updateQualityMetrics() {\n        const history = this.state.conversationHistory;\n        const agents = this.state.activeAgents;\n        // Participation balance (how evenly agents participated)\n        const participationCounts = agents.reduce((acc, agent)=>{\n            acc[agent] = history.filter((turn)=>turn.agent === agent).length;\n            return acc;\n        }, {});\n        const participationValues = Object.values(participationCounts);\n        const avgParticipation = participationValues.reduce((a, b)=>a + b, 0) / participationValues.length;\n        const participationVariance = participationValues.reduce((acc, val)=>acc + Math.pow(val - avgParticipation, 2), 0) / participationValues.length;\n        this.state.qualityMetrics.participationBalance = Math.max(0, 1 - participationVariance / (avgParticipation || 1));\n        // Consensus strength (how many consensus items vs unresolved issues)\n        const totalIssues = this.state.consensusItems.length + this.state.unresolvedIssues.length;\n        this.state.qualityMetrics.consensusStrength = totalIssues > 0 ? this.state.consensusItems.length / totalIssues : 0;\n        // Iterative improvement (are later messages building on earlier ones?)\n        const responseConnections = history.filter((turn)=>turn.respondingTo).length;\n        this.state.qualityMetrics.iterativeImprovement = history.length > 0 ? responseConnections / history.length : 0;\n        // Overall score (weighted average)\n        this.state.qualityMetrics.overallScore = this.state.qualityMetrics.participationBalance * 0.3 + this.state.qualityMetrics.consensusStrength * 0.4 + this.state.qualityMetrics.iterativeImprovement * 0.3;\n        console.log(`[Conversation] Quality metrics updated: Overall=${this.state.qualityMetrics.overallScore.toFixed(2)}, Consensus=${this.state.qualityMetrics.consensusStrength.toFixed(2)}, Balance=${this.state.qualityMetrics.participationBalance.toFixed(2)}`);\n    }\n    // Phase management\n    advancePhase() {\n        const phases = [\n            'brainstorming',\n            'debate',\n            'synthesis',\n            'validation',\n            'complete'\n        ];\n        const currentIndex = phases.indexOf(this.state.currentPhase);\n        if (currentIndex < phases.length - 1) {\n            this.state.currentPhase = phases[currentIndex + 1];\n            console.log(`[Conversation] Advanced to phase: ${this.state.currentPhase}`);\n            return true;\n        }\n        return false;\n    }\n    setPhaseProgress(phase, progress) {\n        this.state.phaseProgress[phase] = Math.max(0, Math.min(1, progress));\n    }\n    // Solution management\n    updateAgentSolution(agent, solution) {\n        this.state.currentSolutions[agent] = solution;\n        console.log(`[Conversation] ${agent} updated their solution (${solution.length} chars)`);\n    }\n    setFinalSolution(solution) {\n        this.state.finalSolution = solution;\n        console.log(`[Conversation] Final solution set (${solution.length} chars)`);\n    }\n    // State access\n    getState() {\n        return this.state;\n    }\n    getCurrentPhase() {\n        return this.state.currentPhase;\n    }\n    getUnresolvedIssues() {\n        return [\n            ...this.state.unresolvedIssues\n        ];\n    }\n    getConsensusItems() {\n        return [\n            ...this.state.consensusItems\n        ];\n    }\n    getQualityMetrics() {\n        return {\n            ...this.state.qualityMetrics\n        };\n    }\n    // Check if conversation should continue\n    shouldContinue() {\n        // Continue if we haven't reached max rounds and there are unresolved issues\n        const hasUnresolvedIssues = this.state.unresolvedIssues.length > 0;\n        const withinRoundLimit = this.state.currentRound < this.state.maxRounds;\n        const qualityThreshold = this.state.qualityMetrics.overallScore >= 0.7;\n        return withinRoundLimit && (hasUnresolvedIssues || !qualityThreshold);\n    }\n    nextRound() {\n        this.state.currentRound++;\n        console.log(`[Conversation] Starting round ${this.state.currentRound}/${this.state.maxRounds}`);\n    }\n    // Debate-specific round management\n    nextDebateRound() {\n        this.state.debateRounds++;\n        console.log(`[Conversation] Starting debate round ${this.state.debateRounds}/${this.state.maxDebateRounds}`);\n    }\n    hasReachedMaxDebateRounds() {\n        return this.state.debateRounds >= this.state.maxDebateRounds;\n    }\n    getDebateRoundInfo() {\n        return {\n            current: this.state.debateRounds,\n            max: this.state.maxDebateRounds\n        };\n    }\n    // Generate conversation summary for context\n    generateContextSummary() {\n        const recentTurns = this.getRecentTurns(3);\n        const unresolvedCount = this.state.unresolvedIssues.length;\n        const consensusCount = this.state.consensusItems.length;\n        const debateInfo = this.state.currentPhase === 'debate' ? `, Debate Round: ${this.state.debateRounds}/${this.state.maxDebateRounds}` : '';\n        return `\n## Conversation Context (Round ${this.state.currentRound}/${this.state.maxRounds}, Phase: ${this.state.currentPhase}${debateInfo})\n\n**Recent Discussion:**\n${recentTurns.map((turn)=>`- ${turn.agentLabel}: ${turn.message.substring(0, 150)}...`).join('\\n')}\n\n**Consensus Reached (${consensusCount} items):**\n${this.state.consensusItems.map((item)=>`- ${item.topic}: ${item.agreedPoint}`).join('\\n')}\n\n**Unresolved Issues (${unresolvedCount} remaining):**\n${this.state.unresolvedIssues.map((issue)=>`- ${issue.description} (${issue.priority} priority)`).join('\\n')}\n\n**Quality Score:** ${this.state.qualityMetrics.overallScore.toFixed(2)}/1.0\n`.trim();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agent-collaboration/ConversationState.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agent-collaboration/ConversationSupervisor.ts":
/*!***************************************************************!*\
  !*** ./src/lib/agent-collaboration/ConversationSupervisor.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationSupervisor: () => (/* binding */ ConversationSupervisor)\n/* harmony export */ });\n/* harmony import */ var _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/langgraph */ \"(rsc)/./node_modules/@langchain/langgraph/index.js\");\n/* harmony import */ var _ConversationState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ConversationState */ \"(rsc)/./src/lib/agent-collaboration/ConversationState.ts\");\n/* harmony import */ var _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ConversationNodes */ \"(rsc)/./src/lib/agent-collaboration/ConversationNodes.ts\");\n// True Agent Collaboration - Conversation Supervisor\n// Orchestrates the entire conversation flow and ensures quality collaboration\n\n\n\nclass ConversationSupervisor {\n    constructor(conversationId, userPrompt, agentApiKeys, customApiConfigId, maxIterations = 10){\n        // Initialize conversation manager\n        const agents = Object.entries(agentApiKeys).map(([id, key])=>({\n                id,\n                label: key.label,\n                role: `Agent ${id.replace('agent_', '')} (${key.label})`\n            }));\n        this.conversationManager = new _ConversationState__WEBPACK_IMPORTED_MODULE_1__.ConversationStateManager(conversationId, userPrompt, agents, Math.ceil(maxIterations / 2), 3 // Max debate rounds - force consensus after 3 rounds of debate\n        );\n        this.agentApiKeys = agentApiKeys;\n        this.customApiConfigId = customApiConfigId;\n        this.maxIterations = maxIterations;\n    }\n    /**\n   * Execute the full conversation workflow\n   */ async executeCollaboration() {\n        console.log(`[Conversation Supervisor] 🚀 Starting true agent collaboration with ${Object.keys(this.agentApiKeys).length} agents`);\n        try {\n            // Build the conversation workflow\n            const workflow = this.buildConversationWorkflow();\n            // Initial state\n            const initialState = {\n                messages: [],\n                conversationManager: this.conversationManager,\n                agentApiKeys: this.agentApiKeys,\n                customApiConfigId: this.customApiConfigId,\n                iterationCount: 0,\n                maxIterations: this.maxIterations,\n                nextAction: 'continue_discussion'\n            };\n            // Execute the workflow\n            console.log(`[Conversation Supervisor] 📋 Executing conversation workflow...`);\n            const finalState = await workflow.invoke(initialState);\n            // Extract results\n            const finalSolution = this.conversationManager.getState().finalSolution;\n            const qualityMetrics = this.conversationManager.getQualityMetrics();\n            const agentNames = Object.values(this.agentApiKeys).map((key)=>key.label);\n            if (!finalSolution) {\n                console.error(`[Conversation Supervisor] ❌ No final solution generated`);\n                return {\n                    success: false,\n                    error: 'Collaboration completed but no final solution was generated',\n                    agentNames\n                };\n            }\n            // Generate the final collaborative response\n            const finalResponse = this.generateCollaborativeResponse(finalSolution, qualityMetrics);\n            console.log(`[Conversation Supervisor] ✅ Collaboration completed successfully!`);\n            console.log(`[Conversation Supervisor] 📊 Quality Score: ${qualityMetrics.overallScore.toFixed(2)}/1.0`);\n            return {\n                success: true,\n                finalResponse,\n                agentNames,\n                qualityMetrics,\n                conversationSummary: this.generateConversationSummary()\n            };\n        } catch (error) {\n            console.error(`[Conversation Supervisor] ❌ Collaboration failed:`, error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error',\n                agentNames: Object.values(this.agentApiKeys).map((key)=>key.label)\n            };\n        }\n    }\n    /**\n   * Build the LangGraph conversation workflow\n   */ buildConversationWorkflow() {\n        const workflow = new _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.StateGraph({\n            channels: {\n                messages: {\n                    value: (x, y)=>x.concat(y),\n                    default: ()=>[]\n                },\n                conversationManager: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.conversationManager\n                },\n                agentApiKeys: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.agentApiKeys\n                },\n                customApiConfigId: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.customApiConfigId\n                },\n                currentAgent: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>undefined\n                },\n                nextAction: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>'continue_discussion'\n                },\n                iterationCount: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>0\n                },\n                maxIterations: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.maxIterations\n                }\n            }\n        });\n        // Add conversation phase nodes\n        workflow.addNode('brainstorming', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createBrainstormingNode());\n        workflow.addNode('debate', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createDebateNode());\n        workflow.addNode('synthesis', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createSynthesisNode());\n        workflow.addNode('validation', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createValidationNode());\n        workflow.addNode('supervisor', this.createSupervisorNode());\n        // Define the conversation flow\n        workflow.addEdge(_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.START, 'brainstorming');\n        workflow.addEdge('brainstorming', 'supervisor');\n        workflow.addEdge('debate', 'supervisor');\n        workflow.addEdge('synthesis', 'supervisor');\n        workflow.addEdge('validation', _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END);\n        // Supervisor decides the next phase\n        workflow.addConditionalEdges('supervisor', this.createConversationRouter(), {\n            'continue_debate': 'debate',\n            'move_to_synthesis': 'synthesis',\n            'move_to_validation': 'validation',\n            'complete': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n        });\n        return workflow.compile();\n    }\n    /**\n   * Create the supervisor node that manages conversation flow\n   */ createSupervisorNode() {\n        return async (state)=>{\n            console.log(`[Supervisor] 👑 Evaluating conversation progress...`);\n            const manager = state.conversationManager;\n            const currentPhase = manager.getCurrentPhase();\n            const qualityMetrics = manager.getQualityMetrics();\n            const unresolvedIssues = manager.getUnresolvedIssues();\n            console.log(`[Supervisor] Phase: ${currentPhase}, Quality: ${qualityMetrics.overallScore.toFixed(2)}, Issues: ${unresolvedIssues.length}`);\n            // Determine next action based on conversation state\n            let nextAction;\n            if (currentPhase === 'brainstorming') {\n                // Move to debate if we have enough ideas\n                const ideaCount = manager.getState().conversationHistory.filter((turn)=>turn.messageType === 'proposal').length;\n                if (ideaCount >= Object.keys(state.agentApiKeys).length) {\n                    manager.advancePhase(); // Advance to debate\n                    nextAction = 'continue_debate';\n                } else {\n                    nextAction = 'continue_debate';\n                }\n            } else if (currentPhase === 'debate') {\n                // Check if we've reached max debate rounds\n                const hasReachedMaxDebateRounds = manager.hasReachedMaxDebateRounds();\n                const debateInfo = manager.getDebateRoundInfo();\n                console.log(`[Supervisor] Debate round ${debateInfo.current}/${debateInfo.max}, Issues: ${unresolvedIssues.length}`);\n                // Force move to synthesis if max debate rounds reached\n                if (hasReachedMaxDebateRounds) {\n                    console.log(`[Supervisor] 🛑 Max debate rounds (${debateInfo.max}) reached, forcing consensus`);\n                    manager.advancePhase(); // Advance to synthesis\n                    nextAction = 'move_to_synthesis';\n                } else {\n                    // Continue debate if we have significant issues and haven't hit limits\n                    const shouldContinueDebate = unresolvedIssues.length > 1 && state.iterationCount < state.maxIterations * 0.6;\n                    if (shouldContinueDebate) {\n                        manager.nextDebateRound(); // Increment debate round counter\n                        nextAction = 'continue_debate';\n                    } else {\n                        manager.advancePhase(); // Advance to synthesis\n                        nextAction = 'move_to_synthesis';\n                    }\n                }\n            } else if (currentPhase === 'synthesis') {\n                // Check if synthesis has been completed (final solution exists)\n                const finalSolution = manager.getState().finalSolution;\n                if (finalSolution) {\n                    manager.advancePhase(); // Advance to validation\n                    nextAction = 'move_to_validation';\n                } else {\n                    // Stay in synthesis until we have a final solution\n                    nextAction = 'move_to_synthesis';\n                }\n            } else {\n                // Complete the conversation\n                nextAction = 'complete';\n            }\n            console.log(`[Supervisor] 🎯 Decision: ${nextAction}`);\n            return {\n                nextAction: nextAction,\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Create the conversation router that decides next steps\n   */ createConversationRouter() {\n        return (state)=>{\n            const manager = state.conversationManager;\n            const currentPhase = manager.getCurrentPhase();\n            const nextAction = state.nextAction;\n            // Safety check for max iterations\n            if (state.iterationCount >= state.maxIterations) {\n                console.log(`[Router] 🛑 Max iterations reached, moving to completion`);\n                return 'complete';\n            }\n            // Route based on supervisor decision\n            switch(nextAction){\n                case 'continue_discussion':\n                    if (currentPhase === 'brainstorming') return 'continue_debate';\n                    if (currentPhase === 'debate') return 'continue_debate';\n                    return 'move_to_synthesis';\n                case 'advance_phase':\n                    if (currentPhase === 'brainstorming') return 'continue_debate';\n                    if (currentPhase === 'debate') return 'move_to_synthesis';\n                    if (currentPhase === 'synthesis') return 'move_to_validation';\n                    return 'complete';\n                case 'complete':\n                    return 'complete';\n                default:\n                    return nextAction || 'complete';\n            }\n        };\n    }\n    /**\n   * Generate the final collaborative response with proper formatting\n   */ generateCollaborativeResponse(finalSolution, qualityMetrics) {\n        const agentCount = Object.keys(this.agentApiKeys).length;\n        const conversationHistory = this.conversationManager.getState().conversationHistory;\n        const consensusItems = this.conversationManager.getConsensusItems();\n        // Extract key contributions from each agent\n        const agentContributions = Object.entries(this.agentApiKeys).map(([agentId, agentKey])=>{\n            const agentTurns = conversationHistory.filter((turn)=>turn.agent === agentId);\n            const keyContribution = agentTurns.find((turn)=>turn.messageType === 'proposal' || turn.messageType === 'synthesis');\n            return {\n                agent: agentKey.label,\n                contribution: keyContribution?.message.substring(0, 200) + '...' || 0\n            };\n        });\n        return `# 🤖 **True Agent Collaboration Complete**\n\n*This response was created through genuine multi-agent collaboration with real-time discussion, debate, and consensus building between ${agentCount} AI agents.*\n\n## 📋 **Collaborative Solution**\n\n${finalSolution}\n\n## 🗣️ **Agent Contributions & Real Discussion**\n\n${agentContributions.map((contrib)=>`**${contrib.agent}:** ${contrib.contribution}`).join('\\n\\n')}\n\n## 🤝 **Genuine Collaboration Process**\n\n**✅ Brainstorming Phase:** All agents shared initial ideas and explored the problem space together\n**✅ Debate Phase:** Agents challenged each other's ideas, identified conflicts, and refined thinking through real discussion\n**✅ Synthesis Phase:** Team collaborated to resolve disagreements and build the final solution\n**✅ Validation Phase:** All agents validated and approved the final collaborative result\n\n## 📊 **Collaboration Quality Metrics**\n\n- **Overall Quality Score:** ${(qualityMetrics.overallScore * 100).toFixed(0)}%\n- **Participation Balance:** ${(qualityMetrics.participationBalance * 100).toFixed(0)}%\n- **Iterative Improvement:** ${(qualityMetrics.iterativeImprovement * 100).toFixed(0)}%\n- **Consensus Strength:** ${(qualityMetrics.consensusStrength * 100).toFixed(0)}%\n\n## 🎯 **Team Consensus Points**\n\n${consensusItems.length > 0 ? consensusItems.map((item)=>`- **${item.topic}:** ${item.agreedPoint}`).join('\\n') : '- The team reached consensus through iterative discussion and debate'}\n\n**Participating Agents:** ${Object.values(this.agentApiKeys).map((key)=>key.label).join(', ')}\n\n**Collaboration Type:** True multi-agent conversation with real-time discussion, conflict resolution, and consensus building\n\n---\n\n*This solution represents genuine collective intelligence achieved through structured agent-to-agent collaboration, not simulated teamwork.*`;\n    }\n    /**\n   * Generate a summary of the conversation for debugging/analysis\n   */ generateConversationSummary() {\n        const state = this.conversationManager.getState();\n        const turnCount = state.conversationHistory.length;\n        const phaseDistribution = state.conversationHistory.reduce((acc, turn)=>{\n            acc[turn.messageType] = (acc[turn.messageType] || 0) + 1;\n            return acc;\n        }, {});\n        return `\nConversation Summary:\n- Total Turns: ${turnCount}\n- Phases Completed: ${state.currentPhase}\n- Consensus Items: ${state.consensusItems.length}\n- Unresolved Issues: ${state.unresolvedIssues.length}\n- Turn Distribution: ${JSON.stringify(phaseDistribution)}\n- Quality Score: ${state.qualityMetrics.overallScore.toFixed(2)}\n- Duration: ${((Date.now() - state.startTime) / 1000).toFixed(1)}s\n`.trim();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agent-collaboration/ConversationSupervisor.ts\n");

/***/ })

};
;