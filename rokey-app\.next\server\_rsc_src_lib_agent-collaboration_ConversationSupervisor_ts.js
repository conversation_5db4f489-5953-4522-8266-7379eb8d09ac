"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_agent-collaboration_ConversationSupervisor_ts";
exports.ids = ["_rsc_src_lib_agent-collaboration_ConversationSupervisor_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/agent-collaboration/ConversationNodes.ts":
/*!**********************************************************!*\
  !*** ./src/lib/agent-collaboration/ConversationNodes.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationNodes: () => (/* binding */ ConversationNodes)\n/* harmony export */ });\n/* harmony import */ var _providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../providers/executeProviderRequest */ \"(rsc)/./src/lib/providers/executeProviderRequest.ts\");\n/* harmony import */ var _encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../encryption */ \"(rsc)/./src/lib/encryption.ts\");\n// True Agent Collaboration - LangGraph Conversation Nodes\n// These nodes handle different phases of real agent-to-agent conversation\n\n\nclass ConversationNodes {\n    /**\n   * Brainstorming Phase Node - Agents share initial ideas and explore the problem space\n   */ static createBrainstormingNode() {\n        return async (state)=>{\n            console.log(`[Brainstorming] Starting brainstorming phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Each agent contributes initial ideas\n            for (const agentId of agentKeys){\n                const agentKey = state.agentApiKeys[agentId];\n                const contextSummary = manager.generateContextSummary();\n                const brainstormPrompt = `${contextSummary}\n\n[BRAINSTORMING PHASE - INITIAL IDEAS]\nYou are ${manager.getState().agentRoles[agentId]} participating in a collaborative brainstorming session.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nYour task in this brainstorming phase:\n1. **ANALYZE** the request and share your initial understanding\n2. **PROPOSE** creative approaches and ideas\n3. **IDENTIFY** key challenges and opportunities\n4. **ASK QUESTIONS** that will help the team understand the problem better\n5. **BUILD** on any ideas already shared by other agents\n\n${manager.getRecentTurns(3).length > 0 ? 'Respond to and build on the ideas already shared by your teammates.' : 'You are starting the brainstorming - share your initial thoughts and ideas.'}\n\nFocus on generating diverse, creative ideas rather than detailed solutions. Be collaborative and engaging!`;\n                try {\n                    const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                        custom_api_config_id: state.customApiConfigId,\n                        messages: [\n                            {\n                                role: 'user',\n                                content: brainstormPrompt\n                            }\n                        ],\n                        temperature: 0.8,\n                        max_tokens: 2000,\n                        stream: false,\n                        role: 'orchestration'\n                    });\n                    if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                        const response = result.responseData.choices[0].message.content;\n                        // Add to conversation\n                        manager.addTurn({\n                            agent: agentId,\n                            agentLabel: agentKey.label,\n                            message: response,\n                            messageType: 'proposal',\n                            respondingTo: manager.getRecentTurns(1)[0]?.id // Respond to most recent if exists\n                        });\n                        console.log(`[Brainstorming] ✅ ${agentKey.label} contributed ideas (${response.length} chars)`);\n                    } else {\n                        console.error(`[Brainstorming] ❌ ${agentKey.label} failed to contribute`);\n                    }\n                } catch (error) {\n                    console.error(`[Brainstorming] Error with ${agentKey.label}:`, error);\n                }\n            }\n            // Update phase progress\n            manager.setPhaseProgress('brainstorming', 1.0);\n            manager.updateQualityMetrics();\n            return {\n                nextAction: 'advance_phase',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Debate Phase Node - Agents challenge ideas, identify conflicts, and refine thinking\n   */ static createDebateNode() {\n        return async (state)=>{\n            console.log(`[Debate] Starting debate phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Ensure we're in debate phase (don't advance here, let supervisor manage phases)\n            console.log(`[Debate] Current phase: ${manager.getCurrentPhase()}`);\n            // Each agent critiques and challenges ideas\n            for (const agentId of agentKeys){\n                const agentKey = state.agentApiKeys[agentId];\n                const contextSummary = manager.generateContextSummary();\n                const recentTurns = manager.getRecentTurns(5);\n                const debatePrompt = `${contextSummary}\n\n[DEBATE PHASE - CRITICAL ANALYSIS]\nYou are ${manager.getState().agentRoles[agentId]} in a critical debate phase.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nRecent Team Discussion:\n${recentTurns.map((turn)=>`${turn.agentLabel}: ${turn.message}`).join('\\n\\n')}\n\nYour task in this debate phase:\n1. **CRITIQUE** ideas shared by other agents - identify flaws, gaps, or concerns\n2. **CHALLENGE** assumptions and approaches that might not work\n3. **DEFEND** good ideas and explain why they're valuable\n4. **IDENTIFY** conflicts between different approaches\n5. **PROPOSE** alternative solutions to problems you identify\n6. **ASK HARD QUESTIONS** that need to be resolved\n\nBe constructively critical - challenge ideas to make them better, not to tear them down. Focus on finding the best path forward through rigorous analysis.`;\n                try {\n                    const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                        custom_api_config_id: state.customApiConfigId,\n                        messages: [\n                            {\n                                role: 'user',\n                                content: debatePrompt\n                            }\n                        ],\n                        temperature: 0.6,\n                        max_tokens: 2500,\n                        stream: false,\n                        role: 'orchestration'\n                    });\n                    if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                        const response = result.responseData.choices[0].message.content;\n                        // Analyze response for MAJOR conflicts and issues only\n                        const hasMajorDisagreement = response.toLowerCase().includes('disagree') && response.toLowerCase().includes('fundamental') || response.toLowerCase().includes('concern') && response.toLowerCase().includes('critical') || response.toLowerCase().includes('problem') && response.toLowerCase().includes('major');\n                        if (hasMajorDisagreement) {\n                            // Extract the main concern/issue - only for significant disagreements\n                            const issueMatch = response.match(/(?:fundamental|critical|major).*?(?:concern|problem|issue|disagree)[^.]*\\.?/i);\n                            if (issueMatch && issueMatch[0].length > 20) {\n                                manager.addUnresolvedIssue(issueMatch[0], [\n                                    agentId\n                                ], 'high' // Only track high-priority issues\n                                );\n                                console.log(`[Debate] 🚨 Major issue identified by ${agentKey.label}: ${issueMatch[0].substring(0, 100)}...`);\n                            }\n                        }\n                        manager.addTurn({\n                            agent: agentId,\n                            agentLabel: agentKey.label,\n                            message: response,\n                            messageType: hasDisagreement ? 'critique' : 'improvement',\n                            respondingTo: recentTurns[recentTurns.length - 1]?.id\n                        });\n                        console.log(`[Debate] ✅ ${agentKey.label} provided critical analysis (${response.length} chars)`);\n                    }\n                } catch (error) {\n                    console.error(`[Debate] Error with ${agentKey.label}:`, error);\n                }\n            }\n            manager.setPhaseProgress('debate', 1.0);\n            manager.updateQualityMetrics();\n            // Check if we need another round of debate or can move to synthesis\n            const unresolvedIssues = manager.getUnresolvedIssues();\n            if (unresolvedIssues.length > 0 && state.iterationCount < state.maxIterations) {\n                return {\n                    nextAction: 'continue_discussion',\n                    iterationCount: state.iterationCount + 1\n                };\n            }\n            return {\n                nextAction: 'advance_phase',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Synthesis Phase Node - Agents collaborate to build the final solution\n   */ static createSynthesisNode() {\n        return async (state)=>{\n            console.log(`[Synthesis] Starting synthesis phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Log current phase (let supervisor manage phase transitions)\n            console.log(`[Synthesis] Current phase: ${manager.getCurrentPhase()}`);\n            // First, actively resolve any remaining issues through consensus building\n            const unresolvedIssues = manager.getUnresolvedIssues();\n            if (unresolvedIssues.length > 0) {\n                console.log(`[Synthesis] 🤝 Building consensus on ${unresolvedIssues.length} remaining issues`);\n                // Resolve ALL issues, not just top 3 - we need consensus\n                for (const issue of unresolvedIssues){\n                    for (const agentId of agentKeys){\n                        const agentKey = state.agentApiKeys[agentId];\n                        const resolutionPrompt = `[CONSENSUS BUILDING - FINAL RESOLUTION]\nYou are ${manager.getState().agentRoles[agentId]} in the FINAL consensus building phase.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nIssue to Resolve: \"${issue.description}\"\n\nIMPORTANT: We must reach consensus NOW. No more debate - provide a DEFINITIVE solution.\n\nYour task:\n1. **ACCEPT** that we need to move forward with a practical solution\n2. **PROPOSE** a specific, implementable resolution\n3. **COMPROMISE** where necessary to reach team agreement\n4. **FOCUS** on what will work best for the user's request\n\nRespond with: \"CONSENSUS: [Your definitive solution that resolves this issue]\"\n\nWe must finalize this issue to proceed with the solution.`;\n                        try {\n                            const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                                custom_api_config_id: state.customApiConfigId,\n                                messages: [\n                                    {\n                                        role: 'user',\n                                        content: resolutionPrompt\n                                    }\n                                ],\n                                temperature: 0.4,\n                                max_tokens: 1500,\n                                stream: false,\n                                role: 'orchestration'\n                            });\n                            if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                                const response = result.responseData.choices[0].message.content;\n                                manager.addTurn({\n                                    agent: agentId,\n                                    agentLabel: agentKey.label,\n                                    message: response,\n                                    messageType: 'agreement',\n                                    respondingTo: undefined\n                                });\n                                // Check if this resolves the issue - look for consensus indicators\n                                if (response.toLowerCase().includes('consensus:') || response.toLowerCase().includes('agree') || response.toLowerCase().includes('solution') || response.toLowerCase().includes('resolved')) {\n                                    const consensusText = response.includes('CONSENSUS:') ? response.split('CONSENSUS:')[1]?.trim() || response : response;\n                                    manager.resolveIssue(issue.id, consensusText);\n                                    console.log(`[Synthesis] ✅ Issue resolved by ${agentKey.label}: ${issue.description.substring(0, 50)}...`);\n                                } else {\n                                    // Force resolution if no clear consensus - we need to move forward\n                                    manager.resolveIssue(issue.id, `Practical resolution: ${response.substring(0, 200)}...`);\n                                    console.log(`[Synthesis] 🔧 Force-resolved issue: ${issue.description.substring(0, 50)}...`);\n                                }\n                            }\n                        } catch (error) {\n                            console.error(`[Synthesis] Error resolving issue with ${agentKey.label}:`, error);\n                        }\n                    }\n                }\n            }\n            // Now build the final collaborative solution\n            console.log(`[Synthesis] Building final collaborative solution`);\n            const leadAgent = agentKeys[0]; // Use first agent as synthesis coordinator\n            const leadAgentKey = state.agentApiKeys[leadAgent];\n            const synthesisPrompt = `[FINAL SOLUTION SYNTHESIS]\nYou are ${manager.getState().agentRoles[leadAgent]} coordinating the final solution synthesis.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nComplete Team Discussion:\n${manager.getState().conversationHistory.map((turn)=>`${turn.agentLabel} (${turn.messageType}): ${turn.message}`).join('\\n\\n')}\n\nTeam Consensus Points:\n${manager.getConsensusItems().map((item)=>`- ${item.topic}: ${item.agreedPoint}`).join('\\n')}\n\nYour task:\n1. **SYNTHESIZE** all the team's ideas into one comprehensive solution\n2. **INCORPORATE** the best elements from each agent's contributions\n3. **ADDRESS** the original request completely and thoroughly\n4. **ENSURE** the solution reflects the team's consensus and collaboration\n5. **PROVIDE** a high-quality, detailed response that showcases the collective intelligence\n\nCreate the final solution that represents the best collaborative thinking of the entire team.`;\n            try {\n                const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(leadAgentKey.provider, leadAgentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(leadAgentKey.encrypted_api_key), {\n                    custom_api_config_id: state.customApiConfigId,\n                    messages: [\n                        {\n                            role: 'user',\n                            content: synthesisPrompt\n                        }\n                    ],\n                    temperature: 0.3,\n                    max_tokens: 4000,\n                    stream: false,\n                    role: 'orchestration'\n                });\n                if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                    const finalSolution = result.responseData.choices[0].message.content;\n                    console.log(`[Synthesis] 🎯 Setting final solution (${finalSolution.length} chars)`);\n                    manager.setFinalSolution(finalSolution);\n                    manager.addTurn({\n                        agent: leadAgent,\n                        agentLabel: leadAgentKey.label,\n                        message: finalSolution,\n                        messageType: 'synthesis'\n                    });\n                    console.log(`[Synthesis] ✅ Final solution synthesized and stored successfully`);\n                } else {\n                    console.error(`[Synthesis] ❌ Failed to get final solution from lead agent:`, result.error);\n                }\n            } catch (error) {\n                console.error(`[Synthesis] ❌ Error creating final solution:`, error);\n            }\n            manager.setPhaseProgress('synthesis', 1.0);\n            manager.updateQualityMetrics();\n            return {\n                nextAction: 'advance_phase',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Validation Phase Node - Team validates the final solution\n   */ static createValidationNode() {\n        return async (state)=>{\n            console.log(`[Validation] Starting validation phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Log current phase (let supervisor manage phase transitions)\n            console.log(`[Validation] Current phase: ${manager.getCurrentPhase()}`);\n            const finalSolution = manager.getState().finalSolution;\n            console.log(`[Validation] 🔍 Checking for final solution... Found: ${finalSolution ? 'YES' : 'NO'}`);\n            if (!finalSolution) {\n                console.error(`[Validation] ❌ No final solution to validate - synthesis may have failed`);\n                console.error(`[Validation] 📊 Current state: Phase=${manager.getCurrentPhase()}, History=${manager.getState().conversationHistory.length} turns`);\n                return {\n                    nextAction: 'complete'\n                };\n            }\n            // Each agent validates the final solution\n            let validationScore = 0;\n            let validationCount = 0;\n            for (const agentId of agentKeys){\n                const agentKey = state.agentApiKeys[agentId];\n                const validationPrompt = `[SOLUTION VALIDATION]\nYou are ${manager.getState().agentRoles[agentId]} validating the team's final solution.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nFinal Team Solution:\n${finalSolution}\n\nYour task:\n1. **EVALUATE** how well the solution addresses the original request\n2. **CHECK** if it incorporates the team's best ideas and consensus\n3. **ASSESS** the quality, completeness, and effectiveness\n4. **IDENTIFY** any remaining gaps or improvements needed\n5. **RATE** the solution on a scale of 1-10\n\nProvide your validation in this format:\n## Validation Assessment\n[Your detailed assessment of the solution]\n\n## Quality Rating: X/10\n[Explanation of your rating]\n\n## Final Approval: YES/NO\n[Whether you approve this as the final solution]`;\n                try {\n                    const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                        custom_api_config_id: state.customApiConfigId,\n                        messages: [\n                            {\n                                role: 'user',\n                                content: validationPrompt\n                            }\n                        ],\n                        temperature: 0.2,\n                        max_tokens: 2000,\n                        stream: false,\n                        role: 'orchestration'\n                    });\n                    if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                        const response = result.responseData.choices[0].message.content;\n                        // Extract rating\n                        const ratingMatch = response.match(/(?:Quality Rating|Rating):\\s*(\\d+)\\/10/i);\n                        if (ratingMatch) {\n                            validationScore += parseInt(ratingMatch[1]);\n                            validationCount++;\n                        }\n                        manager.addTurn({\n                            agent: agentId,\n                            agentLabel: agentKey.label,\n                            message: response,\n                            messageType: 'agreement'\n                        });\n                        console.log(`[Validation] ✅ ${agentKey.label} completed validation`);\n                    }\n                } catch (error) {\n                    console.error(`[Validation] Error with ${agentKey.label}:`, error);\n                }\n            }\n            // Calculate final quality score\n            const avgValidationScore = validationCount > 0 ? validationScore / validationCount : 0;\n            manager.getState().qualityMetrics.noveltyScore = avgValidationScore / 10;\n            manager.setPhaseProgress('validation', 1.0);\n            manager.updateQualityMetrics();\n            console.log(`[Validation] ✅ Validation complete. Average score: ${avgValidationScore.toFixed(1)}/10`);\n            return {\n                nextAction: 'complete',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agent-collaboration/ConversationNodes.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agent-collaboration/ConversationState.ts":
/*!**********************************************************!*\
  !*** ./src/lib/agent-collaboration/ConversationState.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationStateManager: () => (/* binding */ ConversationStateManager)\n/* harmony export */ });\n// True Agent Collaboration - Conversation State Management\n// This manages the real-time conversation state for genuine agent-to-agent communication\nclass ConversationStateManager {\n    constructor(conversationId, userPrompt, agents, maxRounds = 5){\n        this.state = {\n            conversationId,\n            userPrompt,\n            currentPhase: 'brainstorming',\n            conversationHistory: [],\n            activeAgents: agents.map((a)=>a.id),\n            agentRoles: agents.reduce((acc, agent)=>{\n                acc[agent.id] = agent.role || `Agent ${agent.id.replace('agent_', '')} (${agent.label})`;\n                return acc;\n            }, {}),\n            unresolvedIssues: [],\n            consensusItems: [],\n            qualityMetrics: {\n                overallScore: 0,\n                participationBalance: 0,\n                iterativeImprovement: 0,\n                consensusStrength: 0,\n                noveltyScore: 0\n            },\n            phaseProgress: {\n                brainstorming: 0,\n                debate: 0,\n                synthesis: 0,\n                validation: 0\n            },\n            currentSolutions: {},\n            startTime: Date.now(),\n            lastActivity: Date.now(),\n            maxRounds,\n            currentRound: 1\n        };\n    }\n    // Add a new conversation turn\n    addTurn(turn) {\n        const newTurn = {\n            ...turn,\n            id: `turn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: Date.now()\n        };\n        this.state.conversationHistory.push(newTurn);\n        this.state.lastActivity = Date.now();\n        console.log(`[Conversation] ${turn.agentLabel} added ${turn.messageType}: ${turn.message.substring(0, 100)}...`);\n        return newTurn;\n    }\n    // Get conversation turns for a specific agent or responding to a specific turn\n    getTurnsBy(criteria) {\n        return this.state.conversationHistory.filter((turn)=>{\n            if (criteria.agent && turn.agent !== criteria.agent) return false;\n            if (criteria.respondingTo && turn.respondingTo !== criteria.respondingTo) return false;\n            if (criteria.messageType && turn.messageType !== criteria.messageType) return false;\n            return true;\n        });\n    }\n    // Get the latest turns (for context)\n    getRecentTurns(count = 5) {\n        return this.state.conversationHistory.slice(-count);\n    }\n    // Add an unresolved issue\n    addUnresolvedIssue(description, involvedAgents, priority = 'medium') {\n        const issue = {\n            id: `issue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            description,\n            involvedAgents,\n            proposedSolutions: [],\n            priority,\n            createdAt: Date.now()\n        };\n        this.state.unresolvedIssues.push(issue);\n        console.log(`[Conversation] New unresolved issue: ${description}`);\n        return issue;\n    }\n    // Resolve an issue\n    resolveIssue(issueId, solution) {\n        const issueIndex = this.state.unresolvedIssues.findIndex((issue)=>issue.id === issueId);\n        if (issueIndex === -1) return false;\n        const issue = this.state.unresolvedIssues[issueIndex];\n        // Add to consensus items\n        this.addConsensusItem(issue.description, solution, issue.involvedAgents);\n        // Remove from unresolved\n        this.state.unresolvedIssues.splice(issueIndex, 1);\n        console.log(`[Conversation] Resolved issue: ${issue.description} -> ${solution}`);\n        return true;\n    }\n    // Add a consensus item\n    addConsensusItem(topic, agreedPoint, supportingAgents, confidence = 0.8) {\n        const consensus = {\n            topic,\n            agreedPoint,\n            supportingAgents,\n            confidence\n        };\n        this.state.consensusItems.push(consensus);\n        console.log(`[Conversation] New consensus: ${topic} -> ${agreedPoint} (${supportingAgents.length} agents)`);\n        return consensus;\n    }\n    // Update quality metrics\n    updateQualityMetrics() {\n        const history = this.state.conversationHistory;\n        const agents = this.state.activeAgents;\n        // Participation balance (how evenly agents participated)\n        const participationCounts = agents.reduce((acc, agent)=>{\n            acc[agent] = history.filter((turn)=>turn.agent === agent).length;\n            return acc;\n        }, {});\n        const participationValues = Object.values(participationCounts);\n        const avgParticipation = participationValues.reduce((a, b)=>a + b, 0) / participationValues.length;\n        const participationVariance = participationValues.reduce((acc, val)=>acc + Math.pow(val - avgParticipation, 2), 0) / participationValues.length;\n        this.state.qualityMetrics.participationBalance = Math.max(0, 1 - participationVariance / (avgParticipation || 1));\n        // Consensus strength (how many consensus items vs unresolved issues)\n        const totalIssues = this.state.consensusItems.length + this.state.unresolvedIssues.length;\n        this.state.qualityMetrics.consensusStrength = totalIssues > 0 ? this.state.consensusItems.length / totalIssues : 0;\n        // Iterative improvement (are later messages building on earlier ones?)\n        const responseConnections = history.filter((turn)=>turn.respondingTo).length;\n        this.state.qualityMetrics.iterativeImprovement = history.length > 0 ? responseConnections / history.length : 0;\n        // Overall score (weighted average)\n        this.state.qualityMetrics.overallScore = this.state.qualityMetrics.participationBalance * 0.3 + this.state.qualityMetrics.consensusStrength * 0.4 + this.state.qualityMetrics.iterativeImprovement * 0.3;\n        console.log(`[Conversation] Quality metrics updated: Overall=${this.state.qualityMetrics.overallScore.toFixed(2)}, Consensus=${this.state.qualityMetrics.consensusStrength.toFixed(2)}, Balance=${this.state.qualityMetrics.participationBalance.toFixed(2)}`);\n    }\n    // Phase management\n    advancePhase() {\n        const phases = [\n            'brainstorming',\n            'debate',\n            'synthesis',\n            'validation',\n            'complete'\n        ];\n        const currentIndex = phases.indexOf(this.state.currentPhase);\n        if (currentIndex < phases.length - 1) {\n            this.state.currentPhase = phases[currentIndex + 1];\n            console.log(`[Conversation] Advanced to phase: ${this.state.currentPhase}`);\n            return true;\n        }\n        return false;\n    }\n    setPhaseProgress(phase, progress) {\n        this.state.phaseProgress[phase] = Math.max(0, Math.min(1, progress));\n    }\n    // Solution management\n    updateAgentSolution(agent, solution) {\n        this.state.currentSolutions[agent] = solution;\n        console.log(`[Conversation] ${agent} updated their solution (${solution.length} chars)`);\n    }\n    setFinalSolution(solution) {\n        this.state.finalSolution = solution;\n        console.log(`[Conversation] Final solution set (${solution.length} chars)`);\n    }\n    // State access\n    getState() {\n        return this.state;\n    }\n    getCurrentPhase() {\n        return this.state.currentPhase;\n    }\n    getUnresolvedIssues() {\n        return [\n            ...this.state.unresolvedIssues\n        ];\n    }\n    getConsensusItems() {\n        return [\n            ...this.state.consensusItems\n        ];\n    }\n    getQualityMetrics() {\n        return {\n            ...this.state.qualityMetrics\n        };\n    }\n    // Check if conversation should continue\n    shouldContinue() {\n        // Safety limits to prevent infinite loops\n        const maxTotalTurns = 50; // Hard limit on total conversation turns\n        const maxDebateRounds = 3; // Maximum debate rounds per agent\n        const totalTurns = this.state.conversationHistory.length;\n        const debateRounds = this.state.conversationHistory.filter((turn)=>turn.messageType === 'critique').length / this.state.activeAgents.length;\n        // Stop if we hit safety limits\n        if (totalTurns >= maxTotalTurns) {\n            console.log(`[Conversation] 🛑 Stopping: Hit max turns limit (${totalTurns}/${maxTotalTurns})`);\n            return false;\n        }\n        if (debateRounds >= maxDebateRounds) {\n            console.log(`[Conversation] 🛑 Stopping: Hit max debate rounds (${debateRounds.toFixed(1)}/${maxDebateRounds})`);\n            return false;\n        }\n        // Continue if we haven't reached max rounds and there are critical unresolved issues\n        const hasCriticalIssues = this.state.unresolvedIssues.filter((issue)=>issue.priority === 'high').length > 0;\n        const withinRoundLimit = this.state.currentRound < this.state.maxRounds;\n        const qualityThreshold = this.state.qualityMetrics.overallScore >= 0.6; // Lower threshold\n        const shouldContinue = withinRoundLimit && (hasCriticalIssues || !qualityThreshold);\n        if (!shouldContinue) {\n            console.log(`[Conversation] 🏁 Stopping: Rounds=${this.state.currentRound}/${this.state.maxRounds}, CriticalIssues=${hasCriticalIssues}, Quality=${this.state.qualityMetrics.overallScore.toFixed(2)}`);\n        }\n        return shouldContinue;\n    }\n    nextRound() {\n        this.state.currentRound++;\n        console.log(`[Conversation] Starting round ${this.state.currentRound}/${this.state.maxRounds}`);\n    }\n    // Generate conversation summary for context\n    generateContextSummary() {\n        const recentTurns = this.getRecentTurns(3);\n        const unresolvedCount = this.state.unresolvedIssues.length;\n        const consensusCount = this.state.consensusItems.length;\n        return `\n## Conversation Context (Round ${this.state.currentRound}/${this.state.maxRounds}, Phase: ${this.state.currentPhase})\n\n**Recent Discussion:**\n${recentTurns.map((turn)=>`- ${turn.agentLabel}: ${turn.message.substring(0, 150)}...`).join('\\n')}\n\n**Consensus Reached (${consensusCount} items):**\n${this.state.consensusItems.map((item)=>`- ${item.topic}: ${item.agreedPoint}`).join('\\n')}\n\n**Unresolved Issues (${unresolvedCount} remaining):**\n${this.state.unresolvedIssues.map((issue)=>`- ${issue.description} (${issue.priority} priority)`).join('\\n')}\n\n**Quality Score:** ${this.state.qualityMetrics.overallScore.toFixed(2)}/1.0\n`.trim();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agent-collaboration/ConversationState.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agent-collaboration/ConversationSupervisor.ts":
/*!***************************************************************!*\
  !*** ./src/lib/agent-collaboration/ConversationSupervisor.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationSupervisor: () => (/* binding */ ConversationSupervisor)\n/* harmony export */ });\n/* harmony import */ var _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/langgraph */ \"(rsc)/./node_modules/@langchain/langgraph/index.js\");\n/* harmony import */ var _ConversationState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ConversationState */ \"(rsc)/./src/lib/agent-collaboration/ConversationState.ts\");\n/* harmony import */ var _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ConversationNodes */ \"(rsc)/./src/lib/agent-collaboration/ConversationNodes.ts\");\n// True Agent Collaboration - Conversation Supervisor\n// Orchestrates the entire conversation flow and ensures quality collaboration\n\n\n\nclass ConversationSupervisor {\n    constructor(conversationId, userPrompt, agentApiKeys, customApiConfigId, maxIterations = 10){\n        // Initialize conversation manager\n        const agents = Object.entries(agentApiKeys).map(([id, key])=>({\n                id,\n                label: key.label,\n                role: `Agent ${id.replace('agent_', '')} (${key.label})`\n            }));\n        this.conversationManager = new _ConversationState__WEBPACK_IMPORTED_MODULE_1__.ConversationStateManager(conversationId, userPrompt, agents, Math.ceil(maxIterations / 2) // Max rounds is half of max iterations\n        );\n        this.agentApiKeys = agentApiKeys;\n        this.customApiConfigId = customApiConfigId;\n        this.maxIterations = maxIterations;\n    }\n    /**\n   * Execute the full conversation workflow\n   */ async executeCollaboration() {\n        console.log(`[Conversation Supervisor] 🚀 Starting true agent collaboration with ${Object.keys(this.agentApiKeys).length} agents`);\n        try {\n            // Build the conversation workflow\n            const workflow = this.buildConversationWorkflow();\n            // Initial state\n            const initialState = {\n                messages: [],\n                conversationManager: this.conversationManager,\n                agentApiKeys: this.agentApiKeys,\n                customApiConfigId: this.customApiConfigId,\n                iterationCount: 0,\n                maxIterations: this.maxIterations,\n                nextAction: 'continue_discussion'\n            };\n            // Execute the workflow\n            console.log(`[Conversation Supervisor] 📋 Executing conversation workflow...`);\n            const finalState = await workflow.invoke(initialState);\n            // Extract results\n            const finalSolution = this.conversationManager.getState().finalSolution;\n            const qualityMetrics = this.conversationManager.getQualityMetrics();\n            const agentNames = Object.values(this.agentApiKeys).map((key)=>key.label);\n            if (!finalSolution) {\n                console.error(`[Conversation Supervisor] ❌ No final solution generated`);\n                return {\n                    success: false,\n                    error: 'Collaboration completed but no final solution was generated',\n                    agentNames\n                };\n            }\n            // Generate the final collaborative response\n            const finalResponse = this.generateCollaborativeResponse(finalSolution, qualityMetrics);\n            console.log(`[Conversation Supervisor] ✅ Collaboration completed successfully!`);\n            console.log(`[Conversation Supervisor] 📊 Quality Score: ${qualityMetrics.overallScore.toFixed(2)}/1.0`);\n            return {\n                success: true,\n                finalResponse,\n                agentNames,\n                qualityMetrics,\n                conversationSummary: this.generateConversationSummary()\n            };\n        } catch (error) {\n            console.error(`[Conversation Supervisor] ❌ Collaboration failed:`, error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error',\n                agentNames: Object.values(this.agentApiKeys).map((key)=>key.label)\n            };\n        }\n    }\n    /**\n   * Build the LangGraph conversation workflow\n   */ buildConversationWorkflow() {\n        const workflow = new _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.StateGraph({\n            channels: {\n                messages: {\n                    value: (x, y)=>x.concat(y),\n                    default: ()=>[]\n                },\n                conversationManager: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.conversationManager\n                },\n                agentApiKeys: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.agentApiKeys\n                },\n                customApiConfigId: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.customApiConfigId\n                },\n                currentAgent: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>undefined\n                },\n                nextAction: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>'continue_discussion'\n                },\n                iterationCount: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>0\n                },\n                maxIterations: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.maxIterations\n                }\n            }\n        });\n        // Add conversation phase nodes\n        workflow.addNode('brainstorming', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createBrainstormingNode());\n        workflow.addNode('debate', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createDebateNode());\n        workflow.addNode('synthesis', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createSynthesisNode());\n        workflow.addNode('validation', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createValidationNode());\n        workflow.addNode('supervisor', this.createSupervisorNode());\n        // Define the conversation flow\n        workflow.addEdge(_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.START, 'brainstorming');\n        workflow.addEdge('brainstorming', 'supervisor');\n        workflow.addEdge('debate', 'supervisor');\n        workflow.addEdge('synthesis', 'supervisor');\n        workflow.addEdge('validation', _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END);\n        // Supervisor decides the next phase\n        workflow.addConditionalEdges('supervisor', this.createConversationRouter(), {\n            'continue_debate': 'debate',\n            'move_to_synthesis': 'synthesis',\n            'move_to_validation': 'validation',\n            'complete': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n        });\n        return workflow.compile();\n    }\n    /**\n   * Create the supervisor node that manages conversation flow\n   */ createSupervisorNode() {\n        return async (state)=>{\n            console.log(`[Supervisor] 👑 Evaluating conversation progress...`);\n            const manager = state.conversationManager;\n            const currentPhase = manager.getCurrentPhase();\n            const qualityMetrics = manager.getQualityMetrics();\n            const unresolvedIssues = manager.getUnresolvedIssues();\n            console.log(`[Supervisor] Phase: ${currentPhase}, Quality: ${qualityMetrics.overallScore.toFixed(2)}, Issues: ${unresolvedIssues.length}`);\n            // Determine next action based on conversation state\n            let nextAction;\n            if (currentPhase === 'brainstorming') {\n                // Move to debate if we have enough ideas\n                const ideaCount = manager.getState().conversationHistory.filter((turn)=>turn.messageType === 'proposal').length;\n                if (ideaCount >= Object.keys(state.agentApiKeys).length) {\n                    manager.advancePhase(); // Advance to debate\n                    nextAction = 'continue_debate';\n                } else {\n                    nextAction = 'continue_debate';\n                }\n            } else if (currentPhase === 'debate') {\n                // Count debate rounds specifically\n                const debateRounds = manager.getState().conversationHistory.filter((turn)=>turn.messageType === 'critique').length / Object.keys(state.agentApiKeys).length;\n                const maxDebateRounds = 2; // Maximum 2 rounds of debate per agent\n                console.log(`[Supervisor] 📊 Debate analysis: Round ${debateRounds.toFixed(1)}/${maxDebateRounds}, Issues: ${unresolvedIssues.length}, Iteration: ${state.iterationCount}/${state.maxIterations}`);\n                // Force move to synthesis if:\n                // 1. We've had enough debate rounds, OR\n                // 2. We've reached iteration limit, OR\n                // 3. Issues are manageable (≤ 3)\n                const shouldMoveToSynthesis = debateRounds >= maxDebateRounds || state.iterationCount >= state.maxIterations * 0.7 || unresolvedIssues.length <= 3;\n                if (shouldMoveToSynthesis) {\n                    console.log(`[Supervisor] 🎯 Moving to synthesis: DebateRounds=${debateRounds.toFixed(1)}, Issues=${unresolvedIssues.length}, Iterations=${state.iterationCount}`);\n                    manager.advancePhase(); // Advance to synthesis\n                    nextAction = 'move_to_synthesis';\n                } else {\n                    console.log(`[Supervisor] 🔄 Continuing debate: Need more rounds or issue resolution`);\n                    nextAction = 'continue_debate';\n                }\n            } else if (currentPhase === 'synthesis') {\n                // Check if synthesis has been completed (final solution exists)\n                const finalSolution = manager.getState().finalSolution;\n                if (finalSolution) {\n                    manager.advancePhase(); // Advance to validation\n                    nextAction = 'move_to_validation';\n                } else {\n                    // Stay in synthesis until we have a final solution\n                    nextAction = 'move_to_synthesis';\n                }\n            } else {\n                // Complete the conversation\n                nextAction = 'complete';\n            }\n            console.log(`[Supervisor] 🎯 Decision: ${nextAction}`);\n            return {\n                nextAction: nextAction,\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Create the conversation router that decides next steps\n   */ createConversationRouter() {\n        return (state)=>{\n            const manager = state.conversationManager;\n            const currentPhase = manager.getCurrentPhase();\n            const nextAction = state.nextAction;\n            // Safety checks for max iterations and time limits\n            if (state.iterationCount >= state.maxIterations) {\n                console.log(`[Router] 🛑 Max iterations reached (${state.iterationCount}/${state.maxIterations}), forcing completion`);\n                return 'complete';\n            }\n            // Time-based safety check (prevent conversations longer than 5 minutes)\n            const conversationDuration = Date.now() - manager.getState().startTime;\n            const maxDuration = 5 * 60 * 1000; // 5 minutes\n            if (conversationDuration > maxDuration) {\n                console.log(`[Router] ⏰ Max duration reached (${(conversationDuration / 1000).toFixed(1)}s), forcing completion`);\n                return 'complete';\n            }\n            // Turn-based safety check\n            const totalTurns = manager.getState().conversationHistory.length;\n            if (totalTurns > 40) {\n                console.log(`[Router] 📊 Too many turns (${totalTurns}), forcing completion`);\n                return 'complete';\n            }\n            // Route based on supervisor decision\n            switch(nextAction){\n                case 'continue_discussion':\n                    if (currentPhase === 'brainstorming') return 'continue_debate';\n                    if (currentPhase === 'debate') return 'continue_debate';\n                    return 'move_to_synthesis';\n                case 'advance_phase':\n                    if (currentPhase === 'brainstorming') return 'continue_debate';\n                    if (currentPhase === 'debate') return 'move_to_synthesis';\n                    if (currentPhase === 'synthesis') return 'move_to_validation';\n                    return 'complete';\n                case 'complete':\n                    return 'complete';\n                default:\n                    return nextAction || 'complete';\n            }\n        };\n    }\n    /**\n   * Generate the final collaborative response with proper formatting\n   */ generateCollaborativeResponse(finalSolution, qualityMetrics) {\n        const agentCount = Object.keys(this.agentApiKeys).length;\n        const conversationHistory = this.conversationManager.getState().conversationHistory;\n        const consensusItems = this.conversationManager.getConsensusItems();\n        // Extract key contributions from each agent\n        const agentContributions = Object.entries(this.agentApiKeys).map(([agentId, agentKey])=>{\n            const agentTurns = conversationHistory.filter((turn)=>turn.agent === agentId);\n            const keyContribution = agentTurns.find((turn)=>turn.messageType === 'proposal' || turn.messageType === 'synthesis');\n            return {\n                agent: agentKey.label,\n                contribution: keyContribution?.message.substring(0, 200) + '...' || 0\n            };\n        });\n        return `# 🤖 **True Agent Collaboration Complete**\n\n*This response was created through genuine multi-agent collaboration with real-time discussion, debate, and consensus building between ${agentCount} AI agents.*\n\n## 📋 **Collaborative Solution**\n\n${finalSolution}\n\n## 🗣️ **Agent Contributions & Real Discussion**\n\n${agentContributions.map((contrib)=>`**${contrib.agent}:** ${contrib.contribution}`).join('\\n\\n')}\n\n## 🤝 **Genuine Collaboration Process**\n\n**✅ Brainstorming Phase:** All agents shared initial ideas and explored the problem space together\n**✅ Debate Phase:** Agents challenged each other's ideas, identified conflicts, and refined thinking through real discussion\n**✅ Synthesis Phase:** Team collaborated to resolve disagreements and build the final solution\n**✅ Validation Phase:** All agents validated and approved the final collaborative result\n\n## 📊 **Collaboration Quality Metrics**\n\n- **Overall Quality Score:** ${(qualityMetrics.overallScore * 100).toFixed(0)}%\n- **Participation Balance:** ${(qualityMetrics.participationBalance * 100).toFixed(0)}%\n- **Iterative Improvement:** ${(qualityMetrics.iterativeImprovement * 100).toFixed(0)}%\n- **Consensus Strength:** ${(qualityMetrics.consensusStrength * 100).toFixed(0)}%\n\n## 🎯 **Team Consensus Points**\n\n${consensusItems.length > 0 ? consensusItems.map((item)=>`- **${item.topic}:** ${item.agreedPoint}`).join('\\n') : '- The team reached consensus through iterative discussion and debate'}\n\n**Participating Agents:** ${Object.values(this.agentApiKeys).map((key)=>key.label).join(', ')}\n\n**Collaboration Type:** True multi-agent conversation with real-time discussion, conflict resolution, and consensus building\n\n---\n\n*This solution represents genuine collective intelligence achieved through structured agent-to-agent collaboration, not simulated teamwork.*`;\n    }\n    /**\n   * Generate a summary of the conversation for debugging/analysis\n   */ generateConversationSummary() {\n        const state = this.conversationManager.getState();\n        const turnCount = state.conversationHistory.length;\n        const phaseDistribution = state.conversationHistory.reduce((acc, turn)=>{\n            acc[turn.messageType] = (acc[turn.messageType] || 0) + 1;\n            return acc;\n        }, {});\n        return `\nConversation Summary:\n- Total Turns: ${turnCount}\n- Phases Completed: ${state.currentPhase}\n- Consensus Items: ${state.consensusItems.length}\n- Unresolved Issues: ${state.unresolvedIssues.length}\n- Turn Distribution: ${JSON.stringify(phaseDistribution)}\n- Quality Score: ${state.qualityMetrics.overallScore.toFixed(2)}\n- Duration: ${((Date.now() - state.startTime) / 1000).toFixed(1)}s\n`.trim();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agent-collaboration/ConversationSupervisor.ts\n");

/***/ })

};
;