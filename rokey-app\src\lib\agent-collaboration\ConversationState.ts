// True Agent Collaboration - Conversation State Management
// This manages the real-time conversation state for genuine agent-to-agent communication

export interface ConversationTurn {
  id: string;
  agent: string;
  agentLabel: string;
  message: string;
  respondingTo?: string; // ID of the turn this responds to
  messageType: 'proposal' | 'critique' | 'improvement' | 'question' | 'agreement' | 'disagreement' | 'synthesis';
  timestamp: number;
  qualityScore?: number; // 0-1 score for message quality
  citations?: string[]; // References to other agents' points
}

export interface UnresolvedIssue {
  id: string;
  description: string;
  involvedAgents: string[];
  proposedSolutions: string[];
  priority: 'low' | 'medium' | 'high';
  createdAt: number;
}

export interface ConsensusItem {
  topic: string;
  agreedPoint: string;
  supportingAgents: string[];
  confidence: number; // 0-1 how confident we are in this consensus
}

export interface QualityMetrics {
  overallScore: number; // 0-1 overall conversation quality
  participationBalance: number; // How evenly agents participated
  iterativeImprovement: number; // How much ideas improved over time
  consensusStrength: number; // How strong the final consensus is
  noveltyScore: number; // How novel/creative the final solution is
}

export interface CollaborationState {
  // Core conversation tracking
  conversationId: string;
  userPrompt: string;
  currentPhase: 'brainstorming' | 'debate' | 'synthesis' | 'validation' | 'complete';
  conversationHistory: ConversationTurn[];
  
  // Agent management
  activeAgents: string[];
  agentRoles: Record<string, string>; // agent_id -> role description
  currentSpeaker?: string;
  nextSpeaker?: string;
  
  // Issue and consensus tracking
  unresolvedIssues: UnresolvedIssue[];
  consensusItems: ConsensusItem[];
  
  // Quality and progress
  qualityMetrics: QualityMetrics;
  phaseProgress: Record<string, number>; // phase -> completion percentage
  
  // Solution tracking
  currentSolutions: Record<string, string>; // agent -> their current solution
  finalSolution?: string;
  
  // Meta information
  startTime: number;
  lastActivity: number;
  maxRounds: number;
  currentRound: number;
  debateRounds: number; // Track debate-specific rounds
  maxDebateRounds: number; // Limit for debate phase specifically
}

export class ConversationStateManager {
  private state: CollaborationState;

  constructor(
    conversationId: string,
    userPrompt: string,
    agents: Array<{ id: string; label: string; role?: string }>,
    maxRounds: number = 5,
    maxDebateRounds: number = 3
  ) {
    this.state = {
      conversationId,
      userPrompt,
      currentPhase: 'brainstorming',
      conversationHistory: [],
      activeAgents: agents.map(a => a.id),
      agentRoles: agents.reduce((acc, agent) => {
        acc[agent.id] = agent.role || `Agent ${agent.id.replace('agent_', '')} (${agent.label})`;
        return acc;
      }, {} as Record<string, string>),
      unresolvedIssues: [],
      consensusItems: [],
      qualityMetrics: {
        overallScore: 0,
        participationBalance: 0,
        iterativeImprovement: 0,
        consensusStrength: 0,
        noveltyScore: 0
      },
      phaseProgress: {
        brainstorming: 0,
        debate: 0,
        synthesis: 0,
        validation: 0
      },
      currentSolutions: {},
      startTime: Date.now(),
      lastActivity: Date.now(),
      maxRounds,
      currentRound: 1,
      debateRounds: 0,
      maxDebateRounds
    };
  }

  // Add a new conversation turn
  addTurn(turn: Omit<ConversationTurn, 'id' | 'timestamp'>): ConversationTurn {
    const newTurn: ConversationTurn = {
      ...turn,
      id: `turn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now()
    };

    this.state.conversationHistory.push(newTurn);
    this.state.lastActivity = Date.now();
    
    console.log(`[Conversation] ${turn.agentLabel} added ${turn.messageType}: ${turn.message.substring(0, 100)}...`);
    
    return newTurn;
  }

  // Get conversation turns for a specific agent or responding to a specific turn
  getTurnsBy(criteria: { agent?: string; respondingTo?: string; messageType?: ConversationTurn['messageType'] }): ConversationTurn[] {
    return this.state.conversationHistory.filter(turn => {
      if (criteria.agent && turn.agent !== criteria.agent) return false;
      if (criteria.respondingTo && turn.respondingTo !== criteria.respondingTo) return false;
      if (criteria.messageType && turn.messageType !== criteria.messageType) return false;
      return true;
    });
  }

  // Get the latest turns (for context)
  getRecentTurns(count: number = 5): ConversationTurn[] {
    return this.state.conversationHistory.slice(-count);
  }

  // Add an unresolved issue
  addUnresolvedIssue(description: string, involvedAgents: string[], priority: UnresolvedIssue['priority'] = 'medium'): UnresolvedIssue {
    const issue: UnresolvedIssue = {
      id: `issue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      description,
      involvedAgents,
      proposedSolutions: [],
      priority,
      createdAt: Date.now()
    };

    this.state.unresolvedIssues.push(issue);
    console.log(`[Conversation] New unresolved issue: ${description}`);
    
    return issue;
  }

  // Resolve an issue
  resolveIssue(issueId: string, solution: string): boolean {
    const issueIndex = this.state.unresolvedIssues.findIndex(issue => issue.id === issueId);
    if (issueIndex === -1) return false;

    const issue = this.state.unresolvedIssues[issueIndex];
    
    // Add to consensus items
    this.addConsensusItem(issue.description, solution, issue.involvedAgents);
    
    // Remove from unresolved
    this.state.unresolvedIssues.splice(issueIndex, 1);
    
    console.log(`[Conversation] Resolved issue: ${issue.description} -> ${solution}`);
    return true;
  }

  // Add a consensus item
  addConsensusItem(topic: string, agreedPoint: string, supportingAgents: string[], confidence: number = 0.8): ConsensusItem {
    const consensus: ConsensusItem = {
      topic,
      agreedPoint,
      supportingAgents,
      confidence
    };

    this.state.consensusItems.push(consensus);
    console.log(`[Conversation] New consensus: ${topic} -> ${agreedPoint} (${supportingAgents.length} agents)`);
    
    return consensus;
  }

  // Update quality metrics
  updateQualityMetrics(): void {
    const history = this.state.conversationHistory;
    const agents = this.state.activeAgents;

    // Participation balance (how evenly agents participated)
    const participationCounts = agents.reduce((acc, agent) => {
      acc[agent] = history.filter(turn => turn.agent === agent).length;
      return acc;
    }, {} as Record<string, number>);

    const participationValues = Object.values(participationCounts);
    const avgParticipation = participationValues.reduce((a, b) => a + b, 0) / participationValues.length;
    const participationVariance = participationValues.reduce((acc, val) => acc + Math.pow(val - avgParticipation, 2), 0) / participationValues.length;
    this.state.qualityMetrics.participationBalance = Math.max(0, 1 - (participationVariance / (avgParticipation || 1)));

    // Consensus strength (how many consensus items vs unresolved issues)
    const totalIssues = this.state.consensusItems.length + this.state.unresolvedIssues.length;
    this.state.qualityMetrics.consensusStrength = totalIssues > 0 ? this.state.consensusItems.length / totalIssues : 0;

    // Iterative improvement (are later messages building on earlier ones?)
    const responseConnections = history.filter(turn => turn.respondingTo).length;
    this.state.qualityMetrics.iterativeImprovement = history.length > 0 ? responseConnections / history.length : 0;

    // Overall score (weighted average)
    this.state.qualityMetrics.overallScore = (
      this.state.qualityMetrics.participationBalance * 0.3 +
      this.state.qualityMetrics.consensusStrength * 0.4 +
      this.state.qualityMetrics.iterativeImprovement * 0.3
    );

    console.log(`[Conversation] Quality metrics updated: Overall=${this.state.qualityMetrics.overallScore.toFixed(2)}, Consensus=${this.state.qualityMetrics.consensusStrength.toFixed(2)}, Balance=${this.state.qualityMetrics.participationBalance.toFixed(2)}`);
  }

  // Phase management
  advancePhase(): boolean {
    const phases: CollaborationState['currentPhase'][] = ['brainstorming', 'debate', 'synthesis', 'validation', 'complete'];
    const currentIndex = phases.indexOf(this.state.currentPhase);
    
    if (currentIndex < phases.length - 1) {
      this.state.currentPhase = phases[currentIndex + 1];
      console.log(`[Conversation] Advanced to phase: ${this.state.currentPhase}`);
      return true;
    }
    
    return false;
  }

  setPhaseProgress(phase: string, progress: number): void {
    this.state.phaseProgress[phase] = Math.max(0, Math.min(1, progress));
  }

  // Solution management
  updateAgentSolution(agent: string, solution: string): void {
    this.state.currentSolutions[agent] = solution;
    console.log(`[Conversation] ${agent} updated their solution (${solution.length} chars)`);
  }

  setFinalSolution(solution: string): void {
    this.state.finalSolution = solution;
    console.log(`[Conversation] Final solution set (${solution.length} chars)`);
  }

  // State access
  getState(): Readonly<CollaborationState> {
    return this.state;
  }

  getCurrentPhase(): CollaborationState['currentPhase'] {
    return this.state.currentPhase;
  }

  getUnresolvedIssues(): UnresolvedIssue[] {
    return [...this.state.unresolvedIssues];
  }

  getConsensusItems(): ConsensusItem[] {
    return [...this.state.consensusItems];
  }

  getQualityMetrics(): QualityMetrics {
    return { ...this.state.qualityMetrics };
  }

  // Check if conversation should continue
  shouldContinue(): boolean {
    // Continue if we haven't reached max rounds and there are unresolved issues
    const hasUnresolvedIssues = this.state.unresolvedIssues.length > 0;
    const withinRoundLimit = this.state.currentRound < this.state.maxRounds;
    const qualityThreshold = this.state.qualityMetrics.overallScore >= 0.7;
    
    return withinRoundLimit && (hasUnresolvedIssues || !qualityThreshold);
  }

  nextRound(): void {
    this.state.currentRound++;
    console.log(`[Conversation] Starting round ${this.state.currentRound}/${this.state.maxRounds}`);
  }

  // Debate-specific round management
  nextDebateRound(): void {
    this.state.debateRounds++;
    console.log(`[Conversation] Starting debate round ${this.state.debateRounds}/${this.state.maxDebateRounds}`);
  }

  hasReachedMaxDebateRounds(): boolean {
    return this.state.debateRounds >= this.state.maxDebateRounds;
  }

  getDebateRoundInfo(): { current: number; max: number } {
    return { current: this.state.debateRounds, max: this.state.maxDebateRounds };
  }

  // Generate conversation summary for context
  generateContextSummary(): string {
    const recentTurns = this.getRecentTurns(3);
    const unresolvedCount = this.state.unresolvedIssues.length;
    const consensusCount = this.state.consensusItems.length;
    
    const debateInfo = this.state.currentPhase === 'debate'
      ? `, Debate Round: ${this.state.debateRounds}/${this.state.maxDebateRounds}`
      : '';

    return `
## Conversation Context (Round ${this.state.currentRound}/${this.state.maxRounds}, Phase: ${this.state.currentPhase}${debateInfo})

**Recent Discussion:**
${recentTurns.map(turn => `- ${turn.agentLabel}: ${turn.message.substring(0, 150)}...`).join('\n')}

**Consensus Reached (${consensusCount} items):**
${this.state.consensusItems.map(item => `- ${item.topic}: ${item.agreedPoint}`).join('\n')}

**Unresolved Issues (${unresolvedCount} remaining):**
${this.state.unresolvedIssues.map(issue => `- ${issue.description} (${issue.priority} priority)`).join('\n')}

**Quality Score:** ${this.state.qualityMetrics.overallScore.toFixed(2)}/1.0
`.trim();
  }
}
