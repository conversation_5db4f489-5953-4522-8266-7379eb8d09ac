"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/routing-setup/page",{

/***/ "(app-pages-browser)/./src/app/routing-setup/page.tsx":
/*!****************************************!*\
  !*** ./src/app/routing-setup/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingSetupIndexPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronRightIcon,CircleStackIcon,CogIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronRightIcon,CircleStackIcon,CogIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronRightIcon,CircleStackIcon,CogIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronRightIcon,CircleStackIcon,CogIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronRightIcon,CircleStackIcon,CogIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronRightIcon,CircleStackIcon,CogIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useRoutingSetupPrefetch */ \"(app-pages-browser)/./src/hooks/useRoutingSetupPrefetch.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction RoutingSetupIndexPage() {\n    _s();\n    const [configs, setConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Prefetch hook for routing setup pages\n    const { createHoverPrefetch } = (0,_hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_3__.useRoutingSetupPrefetch)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutingSetupIndexPage.useEffect\": ()=>{\n            async function fetchConfigs() {\n                setIsLoading(true);\n                setError(null);\n                try {\n                    const response = await fetch('/api/custom-configs');\n                    if (!response.ok) {\n                        const errorData = await response.json();\n                        throw new Error(errorData.error || 'Failed to fetch configurations');\n                    }\n                    const data = await response.json();\n                    setConfigs(data);\n                } catch (err) {\n                    setError(err.message);\n                    setConfigs([]);\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n            fetchConfigs();\n        }\n    }[\"RoutingSetupIndexPage.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-white mb-2\",\n                                    children: \"Advanced Routing Setup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 max-w-2xl\",\n                                    children: \"Configure intelligent routing strategies for your API configurations with enterprise-grade precision\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin mx-auto mb-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"Loading configurations...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-900/20 border border-red-500/30 rounded-xl p-4 mb-6 backdrop-blur-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 bg-red-500/20 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 text-red-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-red-300 text-sm\",\n                                            children: \"Error Loading Configurations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-400 text-xs mt-0.5\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this),\n                    !isLoading && !error && configs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-xl p-8 max-w-sm mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-orange-500/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-orange-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-h4 text-white mb-2\",\n                                    children: \"No Configurations Found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-body-sm text-gray-400 mb-4 leading-relaxed\",\n                                    children: \"Create your first Custom API Configuration to start setting up intelligent routing strategies\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/my-models\",\n                                    className: \"btn-primary inline-flex items-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Create Configuration\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this),\n                    !isLoading && !error && configs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n                        children: configs.map((config, index)=>{\n                            // Color rotation system - cycle through vibrant colors\n                            const colors = [\n                                {\n                                    bg: 'bg-gradient-to-br from-pink-500 to-rose-600',\n                                    icon: 'text-white',\n                                    text: 'text-white'\n                                },\n                                {\n                                    bg: 'bg-gradient-to-br from-blue-500 to-blue-600',\n                                    icon: 'text-white',\n                                    text: 'text-white'\n                                },\n                                {\n                                    bg: 'bg-gradient-to-br from-emerald-500 to-green-600',\n                                    icon: 'text-white',\n                                    text: 'text-white'\n                                },\n                                {\n                                    bg: 'bg-gradient-to-br from-amber-500 to-orange-600',\n                                    icon: 'text-white',\n                                    text: 'text-white'\n                                },\n                                {\n                                    bg: 'bg-gradient-to-br from-purple-500 to-violet-600',\n                                    icon: 'text-white',\n                                    text: 'text-white'\n                                },\n                                {\n                                    bg: 'bg-gradient-to-br from-cyan-500 to-teal-600',\n                                    icon: 'text-white',\n                                    text: 'text-white'\n                                },\n                                {\n                                    bg: 'bg-gradient-to-br from-indigo-500 to-blue-700',\n                                    icon: 'text-white',\n                                    text: 'text-white'\n                                },\n                                {\n                                    bg: 'bg-gradient-to-br from-red-500 to-pink-600',\n                                    icon: 'text-white',\n                                    text: 'text-white'\n                                }\n                            ];\n                            const colorScheme = colors[index % colors.length];\n                            // Icon selection based on routing strategy\n                            const getStrategyIcon = (strategy)=>{\n                                switch(strategy){\n                                    case 'intelligent_role':\n                                        return _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n                                    case 'complexity_round_robin':\n                                        return _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                    case 'strict_fallback':\n                                        return _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                                    case 'auto_optimal':\n                                        return _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n                                    default:\n                                        return _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n                                }\n                            };\n                            const StrategyIcon = getStrategyIcon(config.routing_strategy || 'none');\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/routing-setup/\".concat(config.id, \"?from=routing-setup\"),\n                                className: \"group block transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl\",\n                                ...createHoverPrefetch(config.id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-2xl shadow-lg overflow-hidden h-80 hover:border-gray-700/50 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(colorScheme.bg, \" p-8 h-48 flex flex-col items-center justify-center relative\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StrategyIcon, {\n                                                        className: \"w-10 h-10 \".concat(colorScheme.icon)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-bold \".concat(colorScheme.text),\n                                                        children: config.routing_strategy === 'none' || !config.routing_strategy ? 'Default' : config.routing_strategy === 'intelligent_role' ? 'Smart Role' : config.routing_strategy === 'complexity_round_robin' ? 'Complexity' : config.routing_strategy === 'strict_fallback' ? 'Fallback' : config.routing_strategy\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronRightIcon_CircleStackIcon_CogIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(colorScheme.text, \" absolute top-4 right-4 group-hover:translate-x-1 transition-transform duration-300\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 h-32 flex flex-col justify-between bg-gray-900/70\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-bold text-white group-hover:text-gray-200 transition-colors duration-200 line-clamp-2 leading-tight\",\n                                                            children: config.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 mt-2\",\n                                                            children: \"Advanced routing configuration with intelligent strategies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 font-medium\",\n                                                            children: [\n                                                                \"Created \",\n                                                                new Date(config.created_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"px-3 py-1 text-xs font-medium text-gray-300 bg-gray-800/50 rounded-md hover:bg-gray-700/50 transition-colors duration-200 border border-gray-700/50\",\n                                                            children: \"Configure\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 19\n                                }, this)\n                            }, config.id, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingSetupIndexPage, \"McFhUYyD4d6DFCsabGrIaoJXs78=\", false, function() {\n    return [\n        _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_3__.useRoutingSetupPrefetch\n    ];\n});\n_c = RoutingSetupIndexPage;\nvar _c;\n$RefreshReg$(_c, \"RoutingSetupIndexPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/routing-setup/page.tsx\n"));

/***/ })

});